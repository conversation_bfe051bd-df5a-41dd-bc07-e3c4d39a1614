import { transform, tryCatch } from '@kdt310722/utils/function'
import { type Address, type Decoder, addDecoderSizePrefix, getAddressDecoder, getStructDecoder, getU32Decoder, getU64Decoder, getUtf8Decoder } from '@solana/kit'
import { HelioProtocolInstruction, identifyHelioProtocolInstruction, parseSinglePaymentInstruction, parseSingleSolPaymentInstruction } from '../generated'
import type { InstructionWithAccountAndData } from '../types/entities'
import { isInstructionWithAccountAndData } from './instructions'

export interface SinglePaymentEvent {
    transferAmount: bigint
    fee: bigint
    sender: Address
    recipient: Address
    label: string
}

export const SINGLE_PAYMENT_EVENT_SIGHASH = Buffer.from([20, 253, 67, 17, 182, 52, 206, 205])

export const SINGLE_PAYMENT_EVENT_DECODER: Decoder<SinglePaymentEvent> = getStructDecoder([
    ['transferAmount', getU64Decoder()],
    ['fee', getU64Decoder()],
    ['sender', getAddressDecoder()],
    ['recipient', getAddressDecoder()],
    ['label', addDecoderSizePrefix(getUtf8Decoder(), getU32Decoder())],
])

export const PROGRAM_DATA_MESSAGE = 'Program data:'

export function * getSinglePaymentEventsFromLogs(logs: string[]) {
    for (const log of logs) {
        if (log.startsWith(PROGRAM_DATA_MESSAGE)) {
            const data = Buffer.from(log.slice(PROGRAM_DATA_MESSAGE.length + 1), 'base64')
            const sighash = data.subarray(0, 8)

            if (sighash.equals(SINGLE_PAYMENT_EVENT_SIGHASH)) {
                yield SINGLE_PAYMENT_EVENT_DECODER.decode(data.subarray(8))
            }
        }
    }
}

export function * getSinglePaymentEventsFromInstructions(instructions: InstructionWithAccountAndData[]): Generator<SinglePaymentEvent> {
    for (const instruction of instructions) {
        if (!isInstructionWithAccountAndData(instruction)) {
            continue
        }

        const identified = tryCatch(() => identifyHelioProtocolInstruction(instruction), null)

        if (identified === HelioProtocolInstruction.SinglePayment) {
            yield transform(parseSinglePaymentInstruction(instruction), (parsed) => ({ label: 'Single', transferAmount: parsed.data.amount, fee: parsed.data.baseFee, sender: parsed.accounts.sender.address, recipient: parsed.accounts.recipient.address }))
        } else if (identified === HelioProtocolInstruction.SingleSolPayment) {
            yield transform(parseSingleSolPaymentInstruction(instruction), (parsed) => ({ label: 'SingleSol', transferAmount: parsed.data.amount, fee: parsed.data.baseFee, sender: parsed.accounts.sender.address, recipient: parsed.accounts.recipient.address }))
        }
    }
}
