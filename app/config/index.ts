import { createConfig } from '../utils/config'
import { address } from '../utils/schemas/address'
import { logger } from './logger'
import { server } from './server'
import { datasources } from './datasources'
import { monitor } from './monitor'
import { compare } from './compare'

export const accounts = address.array().nonempty()

export const config = createConfig({
    logger,
    server,
    accounts,
    datasources,
    monitor,
    compare,
})
