import { z } from 'zod'
import { httpUrl, wsUrl } from '../utils/schemas/urls'

const base = z.object({
    enabled: z.boolean().default(true),
    id: z.string().nonempty(),
    name: z.string().nonempty(),
})

const rpc = base.extend({ type: z.literal('rpc'), url: wsUrl })

const syndica = base.extend({ type: z.literal('syndica'), apiKey: z.string().nonempty() })

const geyser = base.extend({ type: z.literal('grpc'), url: httpUrl, token: z.string().nonempty().optional() })

const thor = base.extend({ type: z.literal('thor'), url: httpUrl, token: z.string().nonempty().optional() })

const corvus = base.extend({ type: z.literal('corvus'), url: httpUrl })

export const datasource = z.discriminatedUnion('type', [rpc, syndica, geyser, thor, corvus])

export const datasources = datasource.array().nonempty()
