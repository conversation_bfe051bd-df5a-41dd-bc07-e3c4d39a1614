import type { Address } from '@solana/kit'
import { RpcWebSocketClient } from '@kdt310722/rpc'
import { LogLevel, highlight } from '@kdt310722/logger'
import { join } from '@kdt310722/utils/buffer'
import { isString } from '@kdt310722/utils/string'
import { tap } from '@kdt310722/utils/function'
import { notNullish } from '@kdt310722/utils/common'
import PQueue from 'p-queue'
import { sum } from '@kdt310722/utils/array'
import { formatDisconnectReason } from '../core/server'
import type { TransactionLogs } from '../types/entities'
import { Datasource, type LogsHandler } from './datasource'

export interface Subscription {
    accounts: Address[]
    onLogs: LogsHandler
}

export abstract class RpcDatasource<TParams> extends Datasource {
    protected readonly client: RpcWebSocketClient
    protected readonly queue: PQueue

    protected incrementId = 0
    protected subscriptions: Record<number, Subscription> = {}
    protected subscriptionIds: Record<number | string, number> = {}
    protected connectPromise?: Promise<void>

    public constructor(public readonly id: string, public readonly name: string, public readonly url: string) {
        super()

        this.client = this.createClient()
        this.queue = new PQueue({ concurrency: 1 })
    }

    public async subscribe(accounts: Address[], onLogs: LogsHandler, id?: number) {
        await this.queue.add(() => this.subscribe_(accounts, onLogs, id))
    }

    protected async subscribe_(accounts: Address[], onLogs: LogsHandler, id?: number) {
        if (!this.client.socket.isConnected) {
            await (this.connectPromise ??= this.client.socket.connect())
        }

        const subscriptionId = id ?? ++this.incrementId

        await this.subscribeLogs(accounts).then((ids) => {
            this.subscriptions[subscriptionId] = { accounts, onLogs }

            for (const id_ of ids) {
                this.subscriptionIds[id_] = subscriptionId
            }
        })
    }

    protected abstract subscribeLogs(accounts: Address[]): Promise<Array<number | string>>

    protected abstract isValidNotification(method: string, params: unknown): params is TParams

    protected abstract getNotificationId(params: TParams): number | string

    protected abstract formatNotification(method: string, params: TParams, receivedAt: number): TransactionLogs | null

    protected handleNotification(method: string, params: unknown) {
        const receivedAt = Date.now()

        if (this.isValidNotification(method, params)) {
            const subscriptionId = this.subscriptionIds[this.getNotificationId(params)]
            const subscription = this.subscriptions[subscriptionId]

            if (notNullish(subscription)) {
                const notification = this.formatNotification(method, params, receivedAt)

                if (notNullish(notification)) {
                    subscription.onLogs(notification)
                }

                return
            }
        }

        this.client.emit('unhandledMessage', JSON.stringify({ method, params }))
    }

    protected handleError(client: RpcWebSocketClient, error: unknown) {
        this.logger.error(`Error occurred in ${highlight(this.name)} server`, error)

        if (client.socket.isConnected) {
            client.socket.disconnect(false).catch((error) => Promise.resolve(this.logger.error(`Failed to disconnect from ${highlight(this.name)} server`, error)).then(() => client.socket.terminate()))
        }
    }

    protected handleConnected(client: RpcWebSocketClient) {
        const events = tap(Object.entries(this.subscriptions), () => this.logger.info(`Connected to ${highlight(this.name)} server!`))

        if (events.length > 0) {
            const timer = tap(this.logger.createTimer(), () => this.logger.info(`Resubscribing to transaction logs of ${highlight(sum(events.map(([, i]) => i.accounts.length)))} accounts...`))

            const onResubscribeSuccess = () => {
                this.logger.stopTimer(timer, 'info', `Resubscribed to all events!`)
                client.socket.resetRetryCount()
            }

            Promise.all(events.map(async ([id, { accounts, onLogs }]) => this.subscribe(accounts, onLogs, Number(id)))).then(onResubscribeSuccess).catch((error) => {
                this.handleError(client, new Error('Resubscribe failed:', { cause: error }))
            })
        }
    }

    protected handleDisconnected(code: number, reason: Buffer, isExplicitlyClosed: boolean) {
        const message = `Disconnected from ${highlight(this.name)} server: ${formatDisconnectReason(code, reason)}`

        if (this.client.socket.isReconnectAttemptReached) {
            this.logger.exit(1, 'fatal', message)
        } else {
            this.logger.log(isExplicitlyClosed ? LogLevel.INFO : LogLevel.WARN, message)
            this.subscriptionIds = {}

            if (isExplicitlyClosed) {
                this.subscriptions = {}
            }
        }
    }

    protected handleUnhandledMessage(message: unknown) {
        this.logger.warn(`Received unhandled message from ${highlight(this.name)} server: ${highlight(Buffer.isBuffer(message) || isString(message) ? join(message) : JSON.stringify(message))}`)
    }

    protected createClient() {
        const client = new RpcWebSocketClient(this.url, { reconnect: { enable: true } })

        client.socket.on('connected', this.handleConnected.bind(this, client))
        client.socket.on('disconnected', this.handleDisconnected.bind(this))
        client.socket.on('reconnect', (attempts) => this.logger.info(`Reconnecting to ${highlight(this.name)} server (attempt: ${highlight(attempts)})...`))
        client.socket.on('error', this.handleError.bind(this, client))

        client.on('error', this.handleError.bind(this, client))
        client.on('unhandledMessage', this.handleUnhandledMessage.bind(this))
        client.on('unhandledRpcMessage', this.handleUnhandledMessage.bind(this))
        client.on('notification', this.handleNotification.bind(this))

        return client
    }
}
