import type { Address } from '@solana/kit'
import type { Logger } from '@kdt310722/logger'
import type { TransactionLogs } from '../types/entities'
import { createChildLogger } from '../core/logger'

export type LogsHandler = (logs: TransactionLogs) => void

export abstract class Datasource {
    public abstract readonly id: string
    public abstract readonly name: string

    #logger?: Logger

    protected get logger() {
        return this.#logger ??= createChildLogger(`datasource:${this.id}`)
    }

    public abstract subscribe(accounts: Address[], onLogs: LogsHandler): Promise<void>
}
