import type { Address, TransactionError } from '@solana/kit'
import { isObject } from '@kdt310722/utils/object'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import type { TransactionLogs } from '../types/entities'
import { RpcDatasource } from './rpc'
import type { SolanaRpcNotification } from './solana-rpc'

export interface SyndicaNotificationContext {
    nodeTime: string
    signature: string
}

export interface SyndicaTransactionMeta {
    err: TransactionError | null
    logMessages: string[]
}

export interface SyndicaTransactionNotification {
    slot: number
    blockTime: number | null
    meta?: SyndicaTransactionMeta | null
}

export type SyndicaTransactionLogsNotification = SolanaRpcNotification<SyndicaTransactionNotification, SyndicaNotificationContext>

export class SyndicaDatasource extends RpcDatasource<SyndicaTransactionLogsNotification> {
    public constructor(id: string, name: string, apiKey: string) {
        super(id, name, `wss://api.syndica.io/api-token/${apiKey}`)
    }

    protected isValidNotification(method: string, params: unknown): params is SyndicaTransactionLogsNotification {
        return method === 'transactionNotification' && isObject(params) && 'subscription' in params && 'result' in params
    }

    protected getNotificationId(params: SyndicaTransactionLogsNotification) {
        return params.subscription
    }

    protected formatNotification(_: string, { result: { context, value: { slot, blockTime, meta } } }: SyndicaTransactionLogsNotification, receivedAt: number): TransactionLogs | null {
        if (isNullish(meta) || notNullish(meta.err)) {
            return null
        }

        return { slot, receivedAt, signature: context.signature, logs: meta.logMessages, blockTime: blockTime ?? undefined, nodeTime: new Date(context.nodeTime).getTime(), source: this.name }
    }

    protected async subscribeLogs(accounts: Address[]) {
        return this.client.call('chainstream.transactionsSubscribe', { network: 'solana-mainnet', verified: false, filter: { excludeVotes: false, commitment: 'processed', accountKeys: { oneOf: accounts } } }).then((id) => [id])
    }
}
