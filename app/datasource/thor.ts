import { ThorStreamClient, type ThorStreamClientOptions, type TransactionEvent } from '@kdt-sol/thorstream-client'
import { isNullish } from '@kdt310722/utils/common'
import type { Address } from '@solana/kit'
import base58 from 'bs58'
import type { TransactionLogs } from '../types/entities'
import { Datasource, type LogsHandler } from './datasource'

export interface ThorDatasourceConfig {
    id: string
    name: string
    url: string
}

export class ThorDatasource extends Datasource {
    public readonly id: string
    public readonly name: string

    protected readonly client: ThorStreamClient

    public constructor({ id, name, url }: ThorDatasourceConfig, options: ThorStreamClientOptions = {}) {
        super()

        this.id = id
        this.name = name
        this.client = this.createClient(url, options)
    }

    public async subscribe(accounts: Address[], onLogs: LogsHandler) {
        this._subscribe(accounts, onLogs).catch((error) => {
            this.logger.forceExit(1, 'fatal', `Error occurred while listening to transactions from Thor server: ${this.name}`, error)
        })

        return Promise.resolve().then(() => void 0)
    }

    protected async _subscribe(accounts: Address[], onLogs: LogsHandler) {
        const transactions = this.client.subscribeWalletTransactions(accounts)

        try {
            for await (const transaction of transactions) {
                if (isNullish(transaction.transaction)) {
                    this.logger.warn('Received a message without transaction', transaction)
                } else {
                    onLogs(this.toTransactionLogs(transaction.transaction))
                }
            }
        } catch (error) {
            this.logger.forceExit(1, 'fatal', `Error occurred while listening to transactions from Thor server: ${this.name}`, error)
        } finally {
            this.logger.forceExit(1, 'fatal', 'Stream ended!')
        }
    }

    protected toTransactionLogs(transaction: TransactionEvent): TransactionLogs {
        return { slot: Number(transaction.slot), signature: base58.encode(transaction.signature), logs: transaction.transactionStatusMeta?.logMessages ?? [], receivedAt: Date.now(), source: this.name }
    }

    protected createClient(url: string, options: ThorStreamClientOptions) {
        return new ThorStreamClient(url, options)
    }
}
