import type { Address, TransactionError } from '@solana/kit'
import { isObject } from '@kdt310722/utils/object'
import { notNullish } from '@kdt310722/utils/common'
import type { TransactionLogs } from '../types/entities'
import { RpcDatasource } from './rpc'

export interface SolanaRpcNotificationContext {
    slot: number
}

export interface SolanaRpcLogsNotification {
    signature: string
    err: TransactionError | null
    logs: string[]
}

export interface SolanaRpcNotification<TValue, TContext = SolanaRpcNotificationContext> {
    subscription: number
    context?: { source?: string, receivedAt?: number }
    result: {
        context: TContext
        value: TValue
    }
}

export type SolanaRpcTransactionLogsNotification = SolanaRpcNotification<SolanaRpcLogsNotification>

export class SolanaRpcDatasource extends RpcDatasource<SolanaRpcTransactionLogsNotification> {
    protected isValidNotification(method: string, params: unknown): params is SolanaRpcTransactionLogsNotification {
        return method === 'logsNotification' && isObject(params) && 'subscription' in params && 'result' in params
    }

    protected getNotificationId(params: SolanaRpcTransactionLogsNotification) {
        return params.subscription
    }

    protected formatNotification(_: string, { context, result: { context: { slot }, value } }: SolanaRpcTransactionLogsNotification, receivedAt: number): TransactionLogs | null {
        if (notNullish(value.err)) {
            return null
        }

        return { slot, receivedAt: context?.receivedAt ?? receivedAt, signature: value.signature, logs: value.logs, source: context?.source ?? this.name }
    }

    protected async subscribeLogs(accounts: Address[]) {
        return Promise.all(accounts.map(async (account) => this.client.call<number>('logsSubscribe', [{ mentions: [account] }, { commitment: 'processed' }])))
    }
}
