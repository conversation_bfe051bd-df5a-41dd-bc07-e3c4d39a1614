import type { GeyserDataContext, GeyserSubscribeUpdate } from '@kdt-sol/geyser-client'
import { GeyserClient } from '@kdt-sol/geyser-client'
import { LogLevel, highlight } from '@kdt310722/logger'
import type { Address } from '@solana/kit'
import { notNullish } from '@kdt310722/utils/common'
import base58 from 'bs58'
import { Datasource, type LogsHandler } from './datasource'

export interface GeyserDatasourceConfig {
    id: string
    name: string
    url: string
    token?: string
}

export class GeyserDatasource extends Datasource {
    public readonly id: string
    public readonly name: string

    protected readonly client: GeyserClient
    protected readonly subscriptions: Record<string, LogsHandler> = {}

    protected connectPromise?: Promise<void>

    public constructor({ id, name, url, token }: GeyserDatasourceConfig) {
        super()

        this.id = id
        this.name = name
        this.client = this.createClient(url, token)
    }

    public async subscribe(accounts: Address[], onLogs: Logs<PERSON>andler) {
        if (!this.client.isConnected) {
            await (this.connectPromise ??= this.client.connect())
        }

        await this.client.subscribe('transactions', { failed: false, vote: false, accountRequired: [], accountExclude: [], accountInclude: accounts }).then((id) => {
            this.subscriptions[id] = onLogs
        })
    }

    protected handleData(subscriptionId: string, data: GeyserSubscribeUpdate, { receivedAt }: GeyserDataContext) {
        if (notNullish(this.subscriptions[subscriptionId]) && notNullish(data.transaction?.transaction?.meta?.logMessages)) {
            const slot = Number(data.transaction.slot)
            const signature = base58.encode(data.transaction.transaction.signature)

            this.subscriptions[subscriptionId]({ slot, signature, logs: data.transaction.transaction.meta.logMessages, receivedAt, nodeTime: data.createdAt?.getTime() })
        }
    }

    protected createClient(url: string, token?: string) {
        const client = new GeyserClient(url, { token, reconnect: { enabled: true, resubscribe: true } })

        client.on('reconnect', (attempt, retriesLeft) => this.logger.info(`Reconnecting to ${highlight(this.name)} server (attempt: ${highlight(attempt)}, retry left: ${highlight(retriesLeft)})...`))
        client.on('reconnectError', (error) => this.printError(`Error occurred while reconnecting to ${highlight(this.name)} server`, error))
        client.on('reconnectFailed', (error) => this.logger.exit(1, 'fatal', `Failed to reconnect to ${highlight(this.name)} server`, error))
        client.on('connected', () => this.logger.info(`Connected to ${highlight(this.name)} server!`))
        client.on('disconnected', (isExplicitly) => this.logger.log(isExplicitly ? LogLevel.INFO : LogLevel.WARN, `Disconnected from ${highlight(this.name)} server!`))
        client.on('resubscribe', (subscriptions) => this.logger.info(`Resubscribing to ${highlight(Object.keys(subscriptions).length)} events on ${highlight(this.name)} server...`))
        client.on('resubscribed', (subscriptions) => this.logger.info(`Resubscribed to ${highlight(Object.keys(subscriptions).length)} events!`))
        client.on('error', (error) => this.printError(`Error occurred on ${highlight(this.name)} client`, error))
        client.on('unhandledMessage', (message) => this.logger.warn(`Received unhandled message from ${highlight(this.name)} server`, message))
        client.on('data', this.handleData.bind(this))

        return client
    }

    protected printError(message: string, error: unknown) {
        if (error instanceof Error) {
            this.logger.error(`${message}: ${highlight(error.message)}`)
        } else {
            this.logger.error(message, error)
        }
    }
}
