import { highlight, message } from '@kdt310722/logger'
import { notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { tap } from '@kdt310722/utils/function'
import { format } from '@kdt310722/utils/number'
import { shorten } from '@kdt310722/utils/string'
import { formatDate } from '@kdt310722/utils/time'
import { config } from '../config'
import type { Datasource } from '../datasource/datasource'
import type { DexScreenerPaymentTransaction, TransactionLogs } from '../types/entities'
import { getSinglePaymentEventsFromInstructions, getSinglePaymentEventsFromLogs } from '../utils/helio'
import { addLatency, datasources } from './datasource'
import { createChildLogger } from './logger'

const logger = createChildLogger('core:logs')
const handledSignatures = new Set<string>()

type LogsEvent = {
    payment: (data: DexScreenerPaymentTransaction) => void
}

export const logs = new Emitter<LogsEvent, true>()
export const displayCount: Record<string, number> = {}

async function updateLatency(datasource: Datasource, signature: string, receivedAt: number, nodeTime?: number) {
    if (notNullish(nodeTime)) {
        await Promise.resolve().then(() => addLatency(datasource.id, receivedAt - nodeTime)).then(() => {
            logger.debug(message(() => `TX: ${highlight(shorten(signature, 4))} - Source: ${highlight(datasource.name)} - Node Time: ${highlight(formatDate(new Date(nodeTime), true))} - Latency: ${highlight(`${format(receivedAt - nodeTime)}ms`)}`))
        })
    }
}

function handleLogs({ signature, slot, logs: logs_, instructions, receivedAt, blockTime, nodeTime, source }: TransactionLogs, datasource: Datasource) {
    Promise.resolve().then(() => updateLatency(datasource, signature, receivedAt, nodeTime))

    if (handledSignatures.has(signature)) {
        return
    }

    handledSignatures.add(signature)

    displayCount[datasource.id] ??= 0
    displayCount[datasource.id]++

    const events = logs_.length === 0 && instructions?.length ? getSinglePaymentEventsFromInstructions(instructions) : getSinglePaymentEventsFromLogs(logs_)
    const base = { signature, slot, blockTime, nodeTime, receivedAt, datasource: source ?? datasource.name }

    let total = 0

    for (const event of events) {
        let symbol: string
        let decimals: number

        if (event.label === 'Single') {
            symbol = 'USDC'
            decimals = 6
        } else {
            symbol = 'SOL'
            decimals = 9
        }

        logs.emit('payment', { ...base, payer: event.sender, recipient: event.recipient, amount: { amount: event.transferAmount, symbol, decimals } })
        total++
    }

    if (total === 0) {
        logger.info(`No payment events found: ${highlight(shorten(signature, 4))} (Source: ${highlight(source ?? datasource.name)}${notNullish(nodeTime) ? `, Node Time: ${highlight(formatDate(new Date(nodeTime), true))}` : ''})`)
    }
}

export async function subscribe() {
    const timer = tap(logger.createTimer(), () => logger.info(`Subscribing to transaction logs of ${highlight(config.accounts.length)} accounts...`))

    for (const datasource of datasources) {
        await datasource.subscribe(config.accounts, (logs) => handleLogs(logs, datasource))
    }

    logger.stopTimer(timer, 'info', 'Subscribed on all datasources!')
}
