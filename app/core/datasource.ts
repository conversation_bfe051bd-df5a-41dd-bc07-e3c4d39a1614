import { LruSet, avg } from '@kdt310722/utils/array'
import { notNullish } from '@kdt310722/utils/common'
import type { z } from 'zod'
import { config } from '../config'
import type { datasource } from '../config/datasources'
import { CorvusDatasource } from '../datasource/corvus'
import { GeyserDatasource } from '../datasource/geyser'
import { SolanaRpcDatasource } from '../datasource/solana-rpc'
import { SyndicaDatasource } from '../datasource/syndica'
import { ThorDatasource } from '../datasource/thor'

export function getDatasource(config: z.infer<typeof datasource>) {
    if (!config.enabled) {
        return
    }

    if (config.type === 'rpc') {
        return new SolanaRpcDatasource(config.id, config.name, config.url)
    }

    if (config.type === 'syndica') {
        return new SyndicaDatasource(config.id, config.name, config.apiKey)
    }

    if (config.type === 'grpc') {
        return new GeyserDatasource(config)
    }

    if (config.type === 'thor') {
        return new ThorDatasource(config, { token: config.token })
    }

    if (config.type === 'corvus') {
        return new CorvusDatasource(config)
    }

    throw new Error(`Unknown datasource type: ${JSON.stringify(config)}`)
}

export const datasources = config.datasources.map(getDatasource).filter(notNullish)

export const latencies: Record<string, LruSet<number>> = Object.fromEntries(datasources.map((datasource) => [datasource.id, new LruSet(config.monitor.maxLatencies)]))

export function addLatency(datasourceId: string, latency: number) {
    latencies[datasourceId].add(latency)
}

export function getAverageLatency(datasourceId: string) {
    if (latencies[datasourceId].size === 0) {
        return Number.NaN
    }

    return avg([...latencies[datasourceId]])
}
