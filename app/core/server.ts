import { type RpcClient, RpcWebSocketServer } from '@kdt310722/rpc'
import { highlight } from '@kdt310722/logger'
import { transform } from '@kdt310722/utils/function'
import { join } from '@kdt310722/utils/buffer'
import { isEmpty } from '@kdt310722/utils/common'
import { pick } from '@kdt310722/utils/object'
import { config } from '../config'
import type { Notifications } from '../types/notifications'
import { createChildLogger } from './logger'

const logger = createChildLogger('common:rpc')
const rpc = new RpcWebSocketServer(config.server.host, config.server.port)

rpc.server.on('listening', () => logger.info(`RPC server is listening on: ${highlight(`ws://${config.server.host}:${config.server.port}`)}`))
rpc.server.on('close', () => logger.info(`RPC server is stopped!`))
rpc.server.on('error', (error) => logger.error('RPC server error', error))
rpc.on('error', (error) => logger.error('RPC server error', error))

const clients = new Set<RpcClient>()

export function formatDisconnectReason(code: number, reason: Buffer | string) {
    return `${highlight(code)} - ${highlight(transform(join(reason), (r) => (isEmpty(r) ? 'NO_REASON' : r)))}`
}

rpc.on('connection', (client) => {
    clients.add(client)
    logger.debug(`New client connected: ${highlight(`#${client.id}`)} - ${highlight(client.ip)}`)

    client.socket.on('close', (code, reason) => {
        clients.delete(client)
        logger.debug(`Client ${highlight(`#${client.id}`)} disconnected: ${formatDisconnectReason(code, reason)}`)
    })
})

function broadcast<TMethod extends keyof Notifications>(method: TMethod, params: Notifications[TMethod]) {
    for (const client of clients) {
        client.notify(method, params).catch((error) => logger.warn('Error while broadcasting notification', error, { client: pick(client, 'id', 'ip'), method, params }))
    }
}

async function start() {
    await Promise.resolve(logger.info('Starting RPC server...')).then(() => rpc.server.start())
}

export const server = Object.assign(rpc, { start, broadcast })
