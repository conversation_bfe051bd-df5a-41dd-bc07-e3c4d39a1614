import { highlight } from '@kdt310722/logger'
import { format } from '@kdt310722/utils/number'
import { config } from '../config'
import { datasources, getAverageLatency } from './datasource'
import { createChildLogger } from './logger'
import { displayCount } from './logs'

const logger = createChildLogger('core:monitor')

let lastMessage: string | undefined

export function printMonitorInfo() {
    const latencies = datasources.map((datasource) => <const>[datasource.id, getAverageLatency(datasource.id)]).filter(([, latency]) => !Number.isNaN(latency)).map(([id, latency]) => `${highlight(id)} - ${highlight(`${format(latency)}ms`)}`).join(', ')

    if (latencies.length === 0) {
        return
    }

    const message = `Avg Latencies: ${latencies}`

    if (lastMessage !== message) {
        logger.info(lastMessage = message)
    }
}

export function printDatasourceStats() {
    logger.info(`Transactions count: ${Object.entries(displayCount).map(([id, count]) => `${highlight(id)} - ${highlight(format(count))}`).join(', ')}`)
}

export function monitor() {
    if (!config.monitor.enabled) {
        return
    }

    setInterval(printMonitorInfo, config.monitor.interval)
    setInterval(printDatasourceStats, config.monitor.interval)
}
