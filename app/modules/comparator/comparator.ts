import type { Logger } from '@kdt310722/logger'
import { notNullish } from '@kdt310722/utils/common'
import { createChildLogger } from '../../core/logger'
import type { Datasource } from '../../datasource/datasource'
import type { CollectedData, DatasourceTransactionInfo } from './collector'

export interface TransactionStats {
    firstByReceivedAt: number
    firstByNodeTime: number
    exclusive: number
    missing: number
    count: number
}

export interface DatasourceStats {
    id: string
    name: string
    latencies?: number[]
    transactions: TransactionStats
}

export interface HandledTransactionData {
    signature: string
    datasources: string[]
    firstByReceivedAt: string
    firstByNodeTime?: string
}

export interface AggregatedData {
    transactionsCount: number
    transactions: CollectedData
    stats: Record<string, DatasourceStats>
}

export class Comparator {
    protected readonly logger: Logger
    protected readonly names: Record<string, string>

    public constructor(protected readonly datasources: Datasource[]) {
        this.logger = createChildLogger('modules:comparator:comparator')
        this.names = Object.fromEntries(datasources.map((datasource) => [datasource.id, datasource.name]))
    }

    public aggregator(data: CollectedData): AggregatedData {
        const stats: Record<string, DatasourceStats> = {}
        const transactionsCount = Object.keys(data).length

        for (const [signature, datasources] of Object.entries(data)) {
            const transactionData = this.prepareTransactionData(signature, datasources)

            for (const [datasourceId, transactionInfo] of Object.entries(datasources)) {
                stats[datasourceId] = this.addDatasourceTransaction(datasourceId, stats[datasourceId] ??= this.createEmptyStats(datasourceId), transactionInfo, transactionData)
            }
        }

        return { transactionsCount, transactions: data, stats: this.sortStats(this.updateMissingTransactions(stats, transactionsCount)) }
    }

    protected sortStats(stats: Record<string, DatasourceStats>) {
        return Object.fromEntries(Object.entries(stats).toSorted((a, b) => b[1].transactions.firstByReceivedAt - a[1].transactions.firstByReceivedAt))
    }

    protected addDatasourceTransaction(datasourceId: string, current: DatasourceStats, { receivedAt, nodeTime }: DatasourceTransactionInfo, data: HandledTransactionData) {
        current.transactions.count++

        if (this.isExclusive(data, datasourceId)) {
            current.transactions.exclusive++
        }

        if (this.isFirstByReceivedAt(data, datasourceId)) {
            current.transactions.firstByReceivedAt++
        }

        if (this.isFirstByNodeTime(data, datasourceId)) {
            current.transactions.firstByNodeTime++
        }

        if (notNullish(nodeTime)) {
            current.latencies ??= []
            current.latencies.push(receivedAt - nodeTime)
        }

        return current
    }

    protected updateMissingTransactions(stats: Record<string, DatasourceStats>, transactionsCount: number) {
        for (const [datasourceId, { transactions: { count } }] of Object.entries(stats)) {
            stats[datasourceId].transactions.missing = transactionsCount - count
        }

        return stats
    }

    protected isExclusive({ datasources }: HandledTransactionData, datasourceId: string) {
        return datasources.length === 1 && datasources[0] === datasourceId
    }

    protected isFirstByReceivedAt({ firstByReceivedAt }: HandledTransactionData, datasourceId: string) {
        return firstByReceivedAt === datasourceId
    }

    protected isFirstByNodeTime({ firstByNodeTime }: HandledTransactionData, datasourceId: string) {
        return firstByNodeTime === datasourceId
    }

    protected prepareTransactionData(signature: string, datasources_: Record<string, DatasourceTransactionInfo>): HandledTransactionData {
        const datasources = Object.keys(datasources_)
        const firstByReceivedAt = datasources.toSorted((a, b) => datasources_[a].receivedAt - datasources_[b].receivedAt)[0]
        const firstByNodeTime = datasources.filter((i) => notNullish(datasources_[i].nodeTime)).toSorted((a, b) => datasources_[a].nodeTime! - datasources_[b].nodeTime!)[0]

        return { signature, datasources, firstByReceivedAt, firstByNodeTime }
    }

    protected createEmptyStats(id: string): DatasourceStats {
        return { id, name: this.names[id], transactions: { firstByReceivedAt: 0, firstByNodeTime: 0, exclusive: 0, missing: 0, count: 0 } }
    }
}
