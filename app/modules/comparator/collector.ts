import { type Address, address } from '@solana/kit'
import { type Logger, highlight } from '@kdt310722/logger'
import { notNullish } from '@kdt310722/utils/common'
import { tap } from '@kdt310722/utils/function'
import { sleep } from '@kdt310722/utils/promise'
import { format } from '@kdt310722/utils/number'
import type { Datasource } from '../../datasource/datasource'
import { createChildLogger } from '../../core/logger'
import type { TransactionLogs } from '../../types/entities'

export interface CollectorOptions {
    account?: Address
}

export const DEFAULT_COLLECTOR_ACCOUNT = address('6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P')

export interface DatasourceTransactionInfo {
    receivedAt: number
    nodeTime?: number
}

export type CollectedData = Record<string, Record<string, DatasourceTransactionInfo>>

export class Collector {
    protected readonly logger: Logger
    protected readonly account: Address

    public constructor(protected readonly datasources: Datasource[], protected readonly collectTime = 60_000, { account = DEFAULT_COLLECTOR_ACCOUNT }: CollectorOptions = {}) {
        this.logger = createChildLogger('modules:comparator:collector')
        this.account = account
    }

    public async collect() {
        const data: CollectedData = {}
        const progress = this.logger.createProgressBar(this.collectTime, { clearOnStop: true, format: '{bar} | {percentage}% | {count}' })

        let isStarted = false

        const handler = (datasource: Datasource, { signature, receivedAt, nodeTime }: TransactionLogs) => {
            if (isStarted) {
                data[signature] ??= {}
                data[signature][datasource.id] = { receivedAt, nodeTime }
            }
        }

        await this.subscribe(handler).then((datasources) => this.logger.info(`Collecting transactions from ${highlight(datasources.length)} datasources...`)).then(() => progress.start(100, { count: '0' })).then(() => sleep(1000)).then(() => {
            isStarted = true
        })

        const interval = setInterval(() => progress.increment(100, { count: format(Object.keys(data).length) }), 100)

        await sleep(this.collectTime).then(() => (isStarted = false)).then(() => clearInterval(interval)).then(() => progress.stop()).then(() => {
            this.logger.info(`Collected ${highlight(format(Object.keys(data).length))} transactions!`)
        })

        return data
    }

    protected async subscribe(handler: (datasource: Datasource, logs: TransactionLogs) => void) {
        const timer = tap(this.logger.createTimer(), () => this.logger.info(`Subscribing to ${highlight(this.datasources.length)} datasources...`))

        const datasources = await Promise.all(this.datasources.map(async (datasource) => (
            datasource.subscribe([this.account], (logs) => handler(datasource, logs)).then(() => datasource).catch((error) => this.handleSubscribeError(datasource, error))
        )))

        return tap(datasources.filter(notNullish), (datasources) => this.logger.stopTimer(timer, 'info', `Subscribed to ${highlight(datasources.length)} datasources!`))
    }

    protected handleSubscribeError(datasource: Datasource, error: unknown) {
        const message = `Datasource ${highlight(datasource.name)} error`

        if (error instanceof Error) {
            this.logger.error(`${message}: ${highlight(error.message)}`)
        } else {
            this.logger.error(message, error)
        }

        return null
    }
}
