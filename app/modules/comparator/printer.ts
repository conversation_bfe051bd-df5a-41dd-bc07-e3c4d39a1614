import { type Logger, highlight } from '@kdt310722/logger'
import { format } from '@kdt310722/utils/number'
import { tap } from '@kdt310722/utils/function'
import { notNullish } from '@kdt310722/utils/common'
import type { Datasource } from '../../datasource/datasource'
import type { AggregatedData, DatasourceStats } from './comparator'
import { compareTime } from './utils'

export class Printer {
    protected readonly names: Record<string, string>

    public constructor(protected readonly logger: Logger, protected readonly collectTime: number, protected readonly datasources: Datasource[]) {
        this.names = Object.fromEntries(datasources.map((datasource) => [datasource.id, datasource.name]))
    }

    public print(aggregatedData: AggregatedData) {
        this.logger.info()
        this.logger.info('=== DATASOURCE COMPARISON REPORT ===')

        this.printInfo(aggregatedData)
        this.printStats(aggregatedData)
        this.printSummary(aggregatedData)
    }

    protected printSummary(aggregatedData: AggregatedData) {
        this.logger.info()
        this.logger.info('=== SUMMARY ===')
        this.logger.info()

        const winner = Object.values(aggregatedData.stats).toSorted((a, b) => b.transactions.firstByReceivedAt - a.transactions.firstByReceivedAt)[0]

        this.logger.info(`Winner: ${highlight(winner.name)}`)
        this.printCompareInfo(aggregatedData, winner.id, 'receivedAt')
        this.printCompareInfo(aggregatedData, winner.id, 'nodeTime')
    }

    protected printCompareInfo({ transactions }: AggregatedData, winnerId: string, column: 'receivedAt' | 'nodeTime') {
        const result = tap(compareTime(transactions, winnerId, column), () => this.logger.info(`Compare with other by ${highlight(column === 'receivedAt' ? 'Received At' : 'Node Time')}:`))

        for (const [datasourceId, { avg, transactionsFaster, transactionsSlower }] of Object.entries(result)) {
            const faster = notNullish(transactionsFaster) ? `TXs faster: ${highlight(format(transactionsFaster.count))} (Avg: ${this.formatMs(transactionsFaster.avg)}, Min: ${this.formatMs(transactionsFaster.min)}, Max: ${this.formatMs(transactionsFaster.max)})` : 'N/A'
            const slower = notNullish(transactionsSlower) ? `TXs slower: ${highlight(format(transactionsSlower.count))} (Avg: ${this.formatMs(transactionsSlower.avg)}, Min: ${this.formatMs(transactionsSlower.min)}, Max: ${this.formatMs(transactionsSlower.max)})` : 'N/A'

            this.logger.info(`  + ${avg >= 0 ? 'Faster' : 'Slower'} than ${highlight(this.names[datasourceId])}: ${this.formatMs(Math.abs(avg))} | ${faster} | ${slower}`)
        }
    }

    protected printInfo({ transactionsCount, stats }: AggregatedData) {
        this.logger.info()
        this.logger.info(`Collection time: ${highlight(`${format(this.collectTime / 1000)}s`)}`)
        this.logger.info(`Total unique transactions: ${highlight(format(transactionsCount))}`)
        this.logger.info(`Total datasources: ${highlight(format(Object.keys(stats).length))}`)
    }

    protected printStats({ transactionsCount, stats }: AggregatedData) {
        for (const datasource of Object.values(stats)) {
            this.printDatasourceStats(datasource, transactionsCount)
        }
    }

    protected printDatasourceStats(datasource: DatasourceStats, transactionsCount: number) {
        this.logger.info()
        this.logger.info(`--- ${highlight(datasource.name)} ---`)

        this.printLatencyStats(datasource)
        this.printTransactionStats(datasource, transactionsCount)
    }

    protected printTransactionStats({ transactions }: DatasourceStats, transactionsCount: number) {
        const firstByReceivedAt = `First by Received At: ${highlight(format(transactions.firstByReceivedAt))} (${highlight(`${this.getPercentage(transactions.firstByReceivedAt, transactionsCount).toFixed(2)}%`)})`
        const firstByNodeTime = `First by Node Time: ${highlight(format(transactions.firstByNodeTime))} (${highlight(`${this.getPercentage(transactions.firstByNodeTime, transactionsCount).toFixed(2)}%`)})`
        const exclusive = `Exclusive: ${highlight(format(transactions.exclusive))}`
        const missing = `Missing: ${highlight(format(transactions.missing))}`

        this.logger.info(`Transactions: ${highlight(format(transactions.count))} (${firstByReceivedAt}, ${firstByNodeTime}, ${exclusive}, ${missing})`)
    }

    protected getPercentage(count: number, total: number) {
        return total > 0 ? (count / total) * 100 : 0
    }

    protected printLatencyStats(datasource: DatasourceStats) {
        const latencies = datasource.latencies?.filter((i) => i >= 0) ?? []

        if (latencies.length === 0) {
            return this.logger.info(`Latency: ${highlight('N/A')}`)
        }

        const avg = latencies.reduce((sum, val) => sum + val, 0) / latencies.length
        const min = Math.min(...latencies)
        const max = Math.max(...latencies)

        this.logger.info(`Latency: ${this.formatMs(avg)} (Min: ${this.formatMs(min)}, Max: ${this.formatMs(max)}, Count: ${highlight(format(latencies.length))})`)
    }

    protected formatMs(ms: number) {
        return highlight(`${format(ms, { maximumFractionDigits: 0 })}ms`)
    }
}
