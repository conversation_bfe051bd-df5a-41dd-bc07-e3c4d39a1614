import { isNullish } from '@kdt310722/utils/common'
import { avg } from '@kdt310722/utils/array'
import type { CollectedData } from './collector'

export interface LatencyStats {
    avg: number
    min: number
    max: number
    count: number
}

export interface CompareTimeResult {
    avg: number
    transactionsFaster?: LatencyStats
    transactionsSlower?: LatencyStats
}

export function getDiffTimes(transactions: CollectedData, winner: string, column: 'receivedAt' | 'nodeTime') {
    const diffs: Record<string, number[]> = {}

    for (const datasources of Object.values(transactions)) {
        if (isNullish(datasources[winner]) || isNullish(datasources[winner][column])) {
            continue
        }

        const winnerTime = datasources[winner][column]

        for (const [datasource, info] of Object.entries(datasources)) {
            if (datasource === winner || isNullish(info[column])) {
                continue
            }

            diffs[datasource] ??= []
            diffs[datasource].push(info[column] - winnerTime)
        }
    }

    return diffs
}

export function getAverageLatency(times: number[]) {
    if (times.length === 0) {
        return Number.NaN
    }

    return avg(times)
}

export function getLatencyStats(times: number[]): LatencyStats | undefined {
    if (times.length === 0) {
        return
    }

    const avg = getAverageLatency(times)
    const min = Math.min(...times)
    const max = Math.max(...times)

    return { avg, min, max, count: times.length }
}

export function compareTime(transactions: CollectedData, winner: string, column: 'receivedAt' | 'nodeTime'): Record<string, CompareTimeResult> {
    const result: Record<string, CompareTimeResult> = {}
    const diffs = getDiffTimes(transactions, winner, column)

    for (const [datasource, times] of Object.entries(diffs)) {
        if (times.every((i) => i === 0)) {
            continue
        }

        const avg = getAverageLatency(times)

        if (Number.isNaN(avg)) {
            continue
        }

        const faster = times.filter((i) => i > 0)
        const slower = times.filter((i) => i < 0).map((i) => Math.abs(i))

        if (faster.length === 0 && slower.length === 0) {
            continue
        }

        result[datasource] = { avg, transactionsFaster: getLatencyStats(faster), transactionsSlower: getLatencyStats(slower) }
    }

    return result
}
