import { tap } from '@kdt310722/utils/function'
import { Collector } from './modules/comparator/collector'
import { datasources } from './core/datasource'
import { Comparator } from './modules/comparator/comparator'
import { createChildLogger } from './core/logger'
import { Printer } from './modules/comparator/printer'
import { config } from './config'

const logger = createChildLogger('comparator')
const collector = new Collector(datasources, config.compare.collectTime, { account: config.compare.account })
const comparator = new Comparator(datasources)
const printer = new Printer(logger, config.compare.collectTime, datasources)

async function main() {
    const data = await collector.collect()
    const timer = tap(logger.createTimer(), () => logger.info('Aggregating data...'))
    const aggregated = tap(comparator.aggregator(data), () => logger.stopTimer(timer, 'info', 'Aggregated data!'))

    printer.print(aggregated)
}

main().then(() => logger.forceExit()).catch((error) => logger.forceExit(error))
