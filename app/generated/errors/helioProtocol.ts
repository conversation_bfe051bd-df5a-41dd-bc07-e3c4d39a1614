/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  isProgramError,
  type Address,
  type SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM,
  type SolanaError,
} from '@solana/kit';
import { HELIO_PROTOCOL_PROGRAM_ADDRESS } from '../programs';

/** InsufficientBalance: Insufficient SOL to pay transfer fees. */
export const HELIO_PROTOCOL_ERROR__INSUFFICIENT_BALANCE = 0x1770; // 6000
/** InvalidChronology: The timestamps must be chronological. */
export const HELIO_PROTOCOL_ERROR__INVALID_CHRONOLOGY = 0x1771; // 6001
/** InvalidAmount: The amount must be positive number. */
export const HELIO_PROTOCOL_ERROR__INVALID_AMOUNT = 0x1772; // 6002
/** InvalidPeriod: The charging period must be shorter then duration time, longer then 10secs. */
export const HELIO_PROTOCOL_ERROR__INVALID_PERIOD = 0x1773; // 6003
/** EmptyAccount: The account is empty, nothing to withdraw. */
export const HELIO_PROTOCOL_ERROR__EMPTY_ACCOUNT = 0x1774; // 6004
/** CancelAuthority: The signer trying to cancel running stream is not a sender! */
export const HELIO_PROTOCOL_ERROR__CANCEL_AUTHORITY = 0x1775; // 6005
/** InvalidNumber: The number is invalid! */
export const HELIO_PROTOCOL_ERROR__INVALID_NUMBER = 0x1776; // 6006
/** InvalidTokenAccount: The token account not provided or has wrong owner. */
export const HELIO_PROTOCOL_ERROR__INVALID_TOKEN_ACCOUNT = 0x1777; // 6007
/** InvalidSplitPaymentData: Remainings amounts and accounts not matching. */
export const HELIO_PROTOCOL_ERROR__INVALID_SPLIT_PAYMENT_DATA = 0x1778; // 6008
/** InvalidPaymentTokenAccount: Invalid payment token account. */
export const HELIO_PROTOCOL_ERROR__INVALID_PAYMENT_TOKEN_ACCOUNT = 0x1779; // 6009
/** InvalidPDASigner: Invalid PDA signer address. */
export const HELIO_PROTOCOL_ERROR__INVALID_P_D_A_SIGNER = 0x177a; // 6010
/** InvalidFeeAccount: Invalid fee account address. */
export const HELIO_PROTOCOL_ERROR__INVALID_FEE_ACCOUNT = 0x177b; // 6011
/** StreamExpired: Stream already expired no changes allowed. */
export const HELIO_PROTOCOL_ERROR__STREAM_EXPIRED = 0x177c; // 6012
/** InvalidTopupAmount: Invalid topup amount (less then base interval amount). */
export const HELIO_PROTOCOL_ERROR__INVALID_TOPUP_AMOUNT = 0x177d; // 6013
/** InvalidFee: Invalid fee, 100 percent or larger. */
export const HELIO_PROTOCOL_ERROR__INVALID_FEE = 0x177e; // 6014
/** UnauthorizedSigner: Signer not authorized to perform operation. */
export const HELIO_PROTOCOL_ERROR__UNAUTHORIZED_SIGNER = 0x177f; // 6015
/** General: General error */
export const HELIO_PROTOCOL_ERROR__GENERAL = 0x1780; // 6016

export type HelioProtocolError =
  | typeof HELIO_PROTOCOL_ERROR__CANCEL_AUTHORITY
  | typeof HELIO_PROTOCOL_ERROR__EMPTY_ACCOUNT
  | typeof HELIO_PROTOCOL_ERROR__GENERAL
  | typeof HELIO_PROTOCOL_ERROR__INSUFFICIENT_BALANCE
  | typeof HELIO_PROTOCOL_ERROR__INVALID_AMOUNT
  | typeof HELIO_PROTOCOL_ERROR__INVALID_CHRONOLOGY
  | typeof HELIO_PROTOCOL_ERROR__INVALID_FEE
  | typeof HELIO_PROTOCOL_ERROR__INVALID_FEE_ACCOUNT
  | typeof HELIO_PROTOCOL_ERROR__INVALID_NUMBER
  | typeof HELIO_PROTOCOL_ERROR__INVALID_PAYMENT_TOKEN_ACCOUNT
  | typeof HELIO_PROTOCOL_ERROR__INVALID_P_D_A_SIGNER
  | typeof HELIO_PROTOCOL_ERROR__INVALID_PERIOD
  | typeof HELIO_PROTOCOL_ERROR__INVALID_SPLIT_PAYMENT_DATA
  | typeof HELIO_PROTOCOL_ERROR__INVALID_TOKEN_ACCOUNT
  | typeof HELIO_PROTOCOL_ERROR__INVALID_TOPUP_AMOUNT
  | typeof HELIO_PROTOCOL_ERROR__STREAM_EXPIRED
  | typeof HELIO_PROTOCOL_ERROR__UNAUTHORIZED_SIGNER;

let helioProtocolErrorMessages: Record<HelioProtocolError, string> | undefined;
if (process.env.NODE_ENV !== 'production') {
  helioProtocolErrorMessages = {
    [HELIO_PROTOCOL_ERROR__CANCEL_AUTHORITY]: `The signer trying to cancel running stream is not a sender!`,
    [HELIO_PROTOCOL_ERROR__EMPTY_ACCOUNT]: `The account is empty, nothing to withdraw.`,
    [HELIO_PROTOCOL_ERROR__GENERAL]: `General error`,
    [HELIO_PROTOCOL_ERROR__INSUFFICIENT_BALANCE]: `Insufficient SOL to pay transfer fees.`,
    [HELIO_PROTOCOL_ERROR__INVALID_AMOUNT]: `The amount must be positive number.`,
    [HELIO_PROTOCOL_ERROR__INVALID_CHRONOLOGY]: `The timestamps must be chronological.`,
    [HELIO_PROTOCOL_ERROR__INVALID_FEE]: `Invalid fee, 100 percent or larger.`,
    [HELIO_PROTOCOL_ERROR__INVALID_FEE_ACCOUNT]: `Invalid fee account address.`,
    [HELIO_PROTOCOL_ERROR__INVALID_NUMBER]: `The number is invalid!`,
    [HELIO_PROTOCOL_ERROR__INVALID_PAYMENT_TOKEN_ACCOUNT]: `Invalid payment token account.`,
    [HELIO_PROTOCOL_ERROR__INVALID_P_D_A_SIGNER]: `Invalid PDA signer address.`,
    [HELIO_PROTOCOL_ERROR__INVALID_PERIOD]: `The charging period must be shorter then duration time, longer then 10secs.`,
    [HELIO_PROTOCOL_ERROR__INVALID_SPLIT_PAYMENT_DATA]: `Remainings amounts and accounts not matching.`,
    [HELIO_PROTOCOL_ERROR__INVALID_TOKEN_ACCOUNT]: `The token account not provided or has wrong owner.`,
    [HELIO_PROTOCOL_ERROR__INVALID_TOPUP_AMOUNT]: `Invalid topup amount (less then base interval amount).`,
    [HELIO_PROTOCOL_ERROR__STREAM_EXPIRED]: `Stream already expired no changes allowed.`,
    [HELIO_PROTOCOL_ERROR__UNAUTHORIZED_SIGNER]: `Signer not authorized to perform operation.`,
  };
}

export function getHelioProtocolErrorMessage(code: HelioProtocolError): string {
  if (process.env.NODE_ENV !== 'production') {
    return (helioProtocolErrorMessages as Record<HelioProtocolError, string>)[
      code
    ];
  }

  return 'Error message not available in production bundles.';
}

export function isHelioProtocolError<
  TProgramErrorCode extends HelioProtocolError,
>(
  error: unknown,
  transactionMessage: {
    instructions: Record<number, { programAddress: Address }>;
  },
  code?: TProgramErrorCode
): error is SolanaError<typeof SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM> &
  Readonly<{ context: Readonly<{ code: TProgramErrorCode }> }> {
  return isProgramError<TProgramErrorCode>(
    error,
    transactionMessage,
    HELIO_PROTOCOL_PROGRAM_ADDRESS,
    code
  );
}
