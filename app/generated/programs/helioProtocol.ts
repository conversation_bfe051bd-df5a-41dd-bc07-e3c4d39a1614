/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  containsBytes,
  fixEncoderSize,
  getBytesEncoder,
  type Address,
  type ReadonlyUint8Array,
} from '@solana/kit';
import {
  type ParsedCancelPaymentInstruction,
  type ParsedCancelSolPaymentInstruction,
  type ParsedCreatePaymentInstruction,
  type ParsedCreateSolPaymentInstruction,
  type ParsedSinglePaymentInstruction,
  type ParsedSingleSolPaymentInstruction,
  type ParsedTopupInstruction,
  type ParsedTopupSolInstruction,
  type ParsedWithdrawInstruction,
  type ParsedWithdrawSolInstruction,
} from '../instructions';

export const HELIO_PROTOCOL_PROGRAM_ADDRESS = '' as Address<''>;

export enum HelioProtocolAccount {
  PaymentAccount,
  SolPaymentAccount,
}

export function identifyHelioProtocolAccount(
  account: { data: ReadonlyUint8Array } | ReadonlyUint8Array
): HelioProtocolAccount {
  const data = 'data' in account ? account.data : account;
  if (
    containsBytes(
      data,
      fixEncoderSize(getBytesEncoder(), 8).encode(
        new Uint8Array([47, 239, 218, 78, 43, 193, 1, 61])
      ),
      0
    )
  ) {
    return HelioProtocolAccount.PaymentAccount;
  }
  if (
    containsBytes(
      data,
      fixEncoderSize(getBytesEncoder(), 8).encode(
        new Uint8Array([69, 131, 203, 233, 93, 55, 130, 146])
      ),
      0
    )
  ) {
    return HelioProtocolAccount.SolPaymentAccount;
  }
  throw new Error(
    'The provided account could not be identified as a helioProtocol account.'
  );
}

export enum HelioProtocolInstruction {
  CreatePayment,
  CreateSolPayment,
  CancelPayment,
  CancelSolPayment,
  Withdraw,
  WithdrawSol,
  SinglePayment,
  SingleSolPayment,
  Topup,
  TopupSol,
}

export function identifyHelioProtocolInstruction(
  instruction: { data: ReadonlyUint8Array } | ReadonlyUint8Array
): HelioProtocolInstruction {
  const data = 'data' in instruction ? instruction.data : instruction;
  if (
    containsBytes(
      data,
      fixEncoderSize(getBytesEncoder(), 8).encode(
        new Uint8Array([28, 81, 85, 253, 7, 223, 154, 42])
      ),
      0
    )
  ) {
    return HelioProtocolInstruction.CreatePayment;
  }
  if (
    containsBytes(
      data,
      fixEncoderSize(getBytesEncoder(), 8).encode(
        new Uint8Array([205, 117, 243, 209, 116, 76, 231, 185])
      ),
      0
    )
  ) {
    return HelioProtocolInstruction.CreateSolPayment;
  }
  if (
    containsBytes(
      data,
      fixEncoderSize(getBytesEncoder(), 8).encode(
        new Uint8Array([217, 129, 71, 37, 216, 193, 38, 33])
      ),
      0
    )
  ) {
    return HelioProtocolInstruction.CancelPayment;
  }
  if (
    containsBytes(
      data,
      fixEncoderSize(getBytesEncoder(), 8).encode(
        new Uint8Array([104, 88, 181, 132, 118, 25, 180, 229])
      ),
      0
    )
  ) {
    return HelioProtocolInstruction.CancelSolPayment;
  }
  if (
    containsBytes(
      data,
      fixEncoderSize(getBytesEncoder(), 8).encode(
        new Uint8Array([183, 18, 70, 156, 148, 109, 161, 34])
      ),
      0
    )
  ) {
    return HelioProtocolInstruction.Withdraw;
  }
  if (
    containsBytes(
      data,
      fixEncoderSize(getBytesEncoder(), 8).encode(
        new Uint8Array([145, 131, 74, 136, 65, 137, 42, 38])
      ),
      0
    )
  ) {
    return HelioProtocolInstruction.WithdrawSol;
  }
  if (
    containsBytes(
      data,
      fixEncoderSize(getBytesEncoder(), 8).encode(
        new Uint8Array([135, 168, 94, 207, 167, 43, 144, 221])
      ),
      0
    )
  ) {
    return HelioProtocolInstruction.SinglePayment;
  }
  if (
    containsBytes(
      data,
      fixEncoderSize(getBytesEncoder(), 8).encode(
        new Uint8Array([197, 96, 243, 163, 157, 0, 244, 13])
      ),
      0
    )
  ) {
    return HelioProtocolInstruction.SingleSolPayment;
  }
  if (
    containsBytes(
      data,
      fixEncoderSize(getBytesEncoder(), 8).encode(
        new Uint8Array([126, 42, 49, 78, 225, 151, 99, 77])
      ),
      0
    )
  ) {
    return HelioProtocolInstruction.Topup;
  }
  if (
    containsBytes(
      data,
      fixEncoderSize(getBytesEncoder(), 8).encode(
        new Uint8Array([79, 110, 51, 253, 35, 240, 52, 176])
      ),
      0
    )
  ) {
    return HelioProtocolInstruction.TopupSol;
  }
  throw new Error(
    'The provided instruction could not be identified as a helioProtocol instruction.'
  );
}

export type ParsedHelioProtocolInstruction<TProgram extends string = ''> =
  | ({
      instructionType: HelioProtocolInstruction.CreatePayment;
    } & ParsedCreatePaymentInstruction<TProgram>)
  | ({
      instructionType: HelioProtocolInstruction.CreateSolPayment;
    } & ParsedCreateSolPaymentInstruction<TProgram>)
  | ({
      instructionType: HelioProtocolInstruction.CancelPayment;
    } & ParsedCancelPaymentInstruction<TProgram>)
  | ({
      instructionType: HelioProtocolInstruction.CancelSolPayment;
    } & ParsedCancelSolPaymentInstruction<TProgram>)
  | ({
      instructionType: HelioProtocolInstruction.Withdraw;
    } & ParsedWithdrawInstruction<TProgram>)
  | ({
      instructionType: HelioProtocolInstruction.WithdrawSol;
    } & ParsedWithdrawSolInstruction<TProgram>)
  | ({
      instructionType: HelioProtocolInstruction.SinglePayment;
    } & ParsedSinglePaymentInstruction<TProgram>)
  | ({
      instructionType: HelioProtocolInstruction.SingleSolPayment;
    } & ParsedSingleSolPaymentInstruction<TProgram>)
  | ({
      instructionType: HelioProtocolInstruction.Topup;
    } & ParsedTopupInstruction<TProgram>)
  | ({
      instructionType: HelioProtocolInstruction.TopupSol;
    } & ParsedTopupSolInstruction<TProgram>);
