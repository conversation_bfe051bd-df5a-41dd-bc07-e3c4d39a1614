/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  assertAccountExists,
  assertAccountsExist,
  combineCodec,
  decodeAccount,
  fetchEncodedAccount,
  fetchEncodedAccounts,
  fixDecoderSize,
  fixEncoderSize,
  getAddressDecoder,
  getAddressEncoder,
  getBooleanDecoder,
  getBooleanEncoder,
  getBytesDecoder,
  getBytesEncoder,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  transformEncoder,
  type Account,
  type Address,
  type Codec,
  type Decoder,
  type EncodedAccount,
  type Encoder,
  type FetchAccountConfig,
  type FetchAccountsConfig,
  type MaybeAccount,
  type MaybeEncodedAccount,
  type ReadonlyUint8Array,
} from '@solana/kit';

export const SOL_PAYMENT_ACCOUNT_DISCRIMINATOR = new Uint8Array([
  69, 131, 203, 233, 93, 55, 130, 146,
]);

export function getSolPaymentAccountDiscriminatorBytes() {
  return fixEncoderSize(getBytesEncoder(), 8).encode(
    SOL_PAYMENT_ACCOUNT_DISCRIMINATOR
  );
}

export type SolPaymentAccount = {
  discriminator: ReadonlyUint8Array;
  /** The total amount to transfer */
  amount: bigint;
  /** The recipents withrawed amount */
  withdrawal: bigint;
  /** The unix timestamp to start the payment at */
  startAt: bigint;
  /** The unix timestamp to end the payment at. If recurrence_interval is zero, then start_at must equal end_at */
  endAt: bigint;
  /** Duration of charged interval in seconds */
  interval: bigint;
  /** The sender party of the payment */
  senderKey: Address;
  /** The recipient party of the payment */
  recipientKey: Address;
  /** Pay fees flag */
  payFees: boolean;
};

export type SolPaymentAccountArgs = {
  /** The total amount to transfer */
  amount: number | bigint;
  /** The recipents withrawed amount */
  withdrawal: number | bigint;
  /** The unix timestamp to start the payment at */
  startAt: number | bigint;
  /** The unix timestamp to end the payment at. If recurrence_interval is zero, then start_at must equal end_at */
  endAt: number | bigint;
  /** Duration of charged interval in seconds */
  interval: number | bigint;
  /** The sender party of the payment */
  senderKey: Address;
  /** The recipient party of the payment */
  recipientKey: Address;
  /** Pay fees flag */
  payFees: boolean;
};

export function getSolPaymentAccountEncoder(): Encoder<SolPaymentAccountArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', fixEncoderSize(getBytesEncoder(), 8)],
      ['amount', getU64Encoder()],
      ['withdrawal', getU64Encoder()],
      ['startAt', getU64Encoder()],
      ['endAt', getU64Encoder()],
      ['interval', getU64Encoder()],
      ['senderKey', getAddressEncoder()],
      ['recipientKey', getAddressEncoder()],
      ['payFees', getBooleanEncoder()],
    ]),
    (value) => ({ ...value, discriminator: SOL_PAYMENT_ACCOUNT_DISCRIMINATOR })
  );
}

export function getSolPaymentAccountDecoder(): Decoder<SolPaymentAccount> {
  return getStructDecoder([
    ['discriminator', fixDecoderSize(getBytesDecoder(), 8)],
    ['amount', getU64Decoder()],
    ['withdrawal', getU64Decoder()],
    ['startAt', getU64Decoder()],
    ['endAt', getU64Decoder()],
    ['interval', getU64Decoder()],
    ['senderKey', getAddressDecoder()],
    ['recipientKey', getAddressDecoder()],
    ['payFees', getBooleanDecoder()],
  ]);
}

export function getSolPaymentAccountCodec(): Codec<
  SolPaymentAccountArgs,
  SolPaymentAccount
> {
  return combineCodec(
    getSolPaymentAccountEncoder(),
    getSolPaymentAccountDecoder()
  );
}

export function decodeSolPaymentAccount<TAddress extends string = string>(
  encodedAccount: EncodedAccount<TAddress>
): Account<SolPaymentAccount, TAddress>;
export function decodeSolPaymentAccount<TAddress extends string = string>(
  encodedAccount: MaybeEncodedAccount<TAddress>
): MaybeAccount<SolPaymentAccount, TAddress>;
export function decodeSolPaymentAccount<TAddress extends string = string>(
  encodedAccount: EncodedAccount<TAddress> | MaybeEncodedAccount<TAddress>
):
  | Account<SolPaymentAccount, TAddress>
  | MaybeAccount<SolPaymentAccount, TAddress> {
  return decodeAccount(
    encodedAccount as MaybeEncodedAccount<TAddress>,
    getSolPaymentAccountDecoder()
  );
}

export async function fetchSolPaymentAccount<TAddress extends string = string>(
  rpc: Parameters<typeof fetchEncodedAccount>[0],
  address: Address<TAddress>,
  config?: FetchAccountConfig
): Promise<Account<SolPaymentAccount, TAddress>> {
  const maybeAccount = await fetchMaybeSolPaymentAccount(rpc, address, config);
  assertAccountExists(maybeAccount);
  return maybeAccount;
}

export async function fetchMaybeSolPaymentAccount<
  TAddress extends string = string,
>(
  rpc: Parameters<typeof fetchEncodedAccount>[0],
  address: Address<TAddress>,
  config?: FetchAccountConfig
): Promise<MaybeAccount<SolPaymentAccount, TAddress>> {
  const maybeAccount = await fetchEncodedAccount(rpc, address, config);
  return decodeSolPaymentAccount(maybeAccount);
}

export async function fetchAllSolPaymentAccount(
  rpc: Parameters<typeof fetchEncodedAccounts>[0],
  addresses: Array<Address>,
  config?: FetchAccountsConfig
): Promise<Account<SolPaymentAccount>[]> {
  const maybeAccounts = await fetchAllMaybeSolPaymentAccount(
    rpc,
    addresses,
    config
  );
  assertAccountsExist(maybeAccounts);
  return maybeAccounts;
}

export async function fetchAllMaybeSolPaymentAccount(
  rpc: Parameters<typeof fetchEncodedAccounts>[0],
  addresses: Array<Address>,
  config?: FetchAccountsConfig
): Promise<MaybeAccount<SolPaymentAccount>[]> {
  const maybeAccounts = await fetchEncodedAccounts(rpc, addresses, config);
  return maybeAccounts.map((maybeAccount) =>
    decodeSolPaymentAccount(maybeAccount)
  );
}

export function getSolPaymentAccountSize(): number {
  return 113;
}
