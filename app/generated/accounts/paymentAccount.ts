/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  assertAccountExists,
  assertAccountsExist,
  combineCodec,
  decodeAccount,
  fetchEncodedAccount,
  fetchEncodedAccounts,
  fixDecoderSize,
  fixEncoderSize,
  getAddressDecoder,
  getAddressEncoder,
  getBooleanDecoder,
  getBooleanEncoder,
  getBytesDecoder,
  getBytesEncoder,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type Account,
  type Address,
  type Codec,
  type Decoder,
  type EncodedAccount,
  type Encoder,
  type FetchAccountConfig,
  type FetchAccountsConfig,
  type MaybeAccount,
  type MaybeEncodedAccount,
  type ReadonlyUint8Array,
} from '@solana/kit';

export const PAYMENT_ACCOUNT_DISCRIMINATOR = new Uint8Array([
  47, 239, 218, 78, 43, 193, 1, 61,
]);

export function getPaymentAccountDiscriminatorBytes() {
  return fixEncoderSize(getBytesEncoder(), 8).encode(
    PAYMENT_ACCOUNT_DISCRIMINATOR
  );
}

export type PaymentAccount = {
  discriminator: ReadonlyUint8Array;
  /** The raw amount to transfer */
  amount: bigint;
  /** The recipents withrawed amount */
  withdrawal: bigint;
  /** The unix timestamp to start the payment at */
  startAt: bigint;
  /** The unix timestamp to end the payment at. If recurrence_interval is zero, then start_at must equal end_at */
  endAt: bigint;
  /** Duration of charged interval in seconds */
  interval: bigint;
  /** The sender party of the payment */
  senderKey: Address;
  /** The sender's token account address */
  senderTokens: Address;
  /** The recipient party of the payment */
  recipientKey: Address;
  /** The recipient's token account address */
  recipientTokens: Address;
  /** The mint (currency) token of the payment */
  mint: Address;
  /** PDA bump (0-255, sent from client on stream creation) */
  bump: number;
  /** Pay fees flag */
  payFees: boolean;
};

export type PaymentAccountArgs = {
  /** The raw amount to transfer */
  amount: number | bigint;
  /** The recipents withrawed amount */
  withdrawal: number | bigint;
  /** The unix timestamp to start the payment at */
  startAt: number | bigint;
  /** The unix timestamp to end the payment at. If recurrence_interval is zero, then start_at must equal end_at */
  endAt: number | bigint;
  /** Duration of charged interval in seconds */
  interval: number | bigint;
  /** The sender party of the payment */
  senderKey: Address;
  /** The sender's token account address */
  senderTokens: Address;
  /** The recipient party of the payment */
  recipientKey: Address;
  /** The recipient's token account address */
  recipientTokens: Address;
  /** The mint (currency) token of the payment */
  mint: Address;
  /** PDA bump (0-255, sent from client on stream creation) */
  bump: number;
  /** Pay fees flag */
  payFees: boolean;
};

export function getPaymentAccountEncoder(): Encoder<PaymentAccountArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', fixEncoderSize(getBytesEncoder(), 8)],
      ['amount', getU64Encoder()],
      ['withdrawal', getU64Encoder()],
      ['startAt', getU64Encoder()],
      ['endAt', getU64Encoder()],
      ['interval', getU64Encoder()],
      ['senderKey', getAddressEncoder()],
      ['senderTokens', getAddressEncoder()],
      ['recipientKey', getAddressEncoder()],
      ['recipientTokens', getAddressEncoder()],
      ['mint', getAddressEncoder()],
      ['bump', getU8Encoder()],
      ['payFees', getBooleanEncoder()],
    ]),
    (value) => ({ ...value, discriminator: PAYMENT_ACCOUNT_DISCRIMINATOR })
  );
}

export function getPaymentAccountDecoder(): Decoder<PaymentAccount> {
  return getStructDecoder([
    ['discriminator', fixDecoderSize(getBytesDecoder(), 8)],
    ['amount', getU64Decoder()],
    ['withdrawal', getU64Decoder()],
    ['startAt', getU64Decoder()],
    ['endAt', getU64Decoder()],
    ['interval', getU64Decoder()],
    ['senderKey', getAddressDecoder()],
    ['senderTokens', getAddressDecoder()],
    ['recipientKey', getAddressDecoder()],
    ['recipientTokens', getAddressDecoder()],
    ['mint', getAddressDecoder()],
    ['bump', getU8Decoder()],
    ['payFees', getBooleanDecoder()],
  ]);
}

export function getPaymentAccountCodec(): Codec<
  PaymentAccountArgs,
  PaymentAccount
> {
  return combineCodec(getPaymentAccountEncoder(), getPaymentAccountDecoder());
}

export function decodePaymentAccount<TAddress extends string = string>(
  encodedAccount: EncodedAccount<TAddress>
): Account<PaymentAccount, TAddress>;
export function decodePaymentAccount<TAddress extends string = string>(
  encodedAccount: MaybeEncodedAccount<TAddress>
): MaybeAccount<PaymentAccount, TAddress>;
export function decodePaymentAccount<TAddress extends string = string>(
  encodedAccount: EncodedAccount<TAddress> | MaybeEncodedAccount<TAddress>
): Account<PaymentAccount, TAddress> | MaybeAccount<PaymentAccount, TAddress> {
  return decodeAccount(
    encodedAccount as MaybeEncodedAccount<TAddress>,
    getPaymentAccountDecoder()
  );
}

export async function fetchPaymentAccount<TAddress extends string = string>(
  rpc: Parameters<typeof fetchEncodedAccount>[0],
  address: Address<TAddress>,
  config?: FetchAccountConfig
): Promise<Account<PaymentAccount, TAddress>> {
  const maybeAccount = await fetchMaybePaymentAccount(rpc, address, config);
  assertAccountExists(maybeAccount);
  return maybeAccount;
}

export async function fetchMaybePaymentAccount<
  TAddress extends string = string,
>(
  rpc: Parameters<typeof fetchEncodedAccount>[0],
  address: Address<TAddress>,
  config?: FetchAccountConfig
): Promise<MaybeAccount<PaymentAccount, TAddress>> {
  const maybeAccount = await fetchEncodedAccount(rpc, address, config);
  return decodePaymentAccount(maybeAccount);
}

export async function fetchAllPaymentAccount(
  rpc: Parameters<typeof fetchEncodedAccounts>[0],
  addresses: Array<Address>,
  config?: FetchAccountsConfig
): Promise<Account<PaymentAccount>[]> {
  const maybeAccounts = await fetchAllMaybePaymentAccount(
    rpc,
    addresses,
    config
  );
  assertAccountsExist(maybeAccounts);
  return maybeAccounts;
}

export async function fetchAllMaybePaymentAccount(
  rpc: Parameters<typeof fetchEncodedAccounts>[0],
  addresses: Array<Address>,
  config?: FetchAccountsConfig
): Promise<MaybeAccount<PaymentAccount>[]> {
  const maybeAccounts = await fetchEncodedAccounts(rpc, addresses, config);
  return maybeAccounts.map((maybeAccount) =>
    decodePaymentAccount(maybeAccount)
  );
}

export function getPaymentAccountSize(): number {
  return 210;
}
