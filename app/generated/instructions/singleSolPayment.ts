/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  fixDecoderSize,
  fixEncoderSize,
  getArrayDecoder,
  getArrayEncoder,
  getBytesDecoder,
  getBytesEncoder,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  transformEncoder,
  type Address,
  type Codec,
  type Decoder,
  type Encoder,
  type IAccountMeta,
  type IAccountSignerMeta,
  type IInstruction,
  type IInstructionWithAccounts,
  type IInstructionWithData,
  type ReadonlyAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
  type WritableSignerAccount,
} from '@solana/kit';
import { HELIO_PROTOCOL_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const SINGLE_SOL_PAYMENT_DISCRIMINATOR = new Uint8Array([
  197, 96, 243, 163, 157, 0, 244, 13,
]);

export function getSingleSolPaymentDiscriminatorBytes() {
  return fixEncoderSize(getBytesEncoder(), 8).encode(
    SINGLE_SOL_PAYMENT_DISCRIMINATOR
  );
}

export type SingleSolPaymentInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountSender extends string | IAccountMeta<string> = string,
  TAccountRecipient extends string | IAccountMeta<string> = string,
  TAccountHelioFeeAccount extends string | IAccountMeta<string> = string,
  TAccountDaoFeeAccount extends string | IAccountMeta<string> = string,
  TAccountSystemProgram extends
    | string
    | IAccountMeta<string> = '11111111111111111111111111111111',
  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],
> = IInstruction<TProgram> &
  IInstructionWithData<Uint8Array> &
  IInstructionWithAccounts<
    [
      TAccountSender extends string
        ? WritableSignerAccount<TAccountSender> &
            IAccountSignerMeta<TAccountSender>
        : TAccountSender,
      TAccountRecipient extends string
        ? WritableAccount<TAccountRecipient>
        : TAccountRecipient,
      TAccountHelioFeeAccount extends string
        ? WritableAccount<TAccountHelioFeeAccount>
        : TAccountHelioFeeAccount,
      TAccountDaoFeeAccount extends string
        ? WritableAccount<TAccountDaoFeeAccount>
        : TAccountDaoFeeAccount,
      TAccountSystemProgram extends string
        ? ReadonlyAccount<TAccountSystemProgram>
        : TAccountSystemProgram,
      ...TRemainingAccounts,
    ]
  >;

export type SingleSolPaymentInstructionData = {
  discriminator: ReadonlyUint8Array;
  amount: bigint;
  baseFee: bigint;
  remainingAmounts: Array<bigint>;
};

export type SingleSolPaymentInstructionDataArgs = {
  amount: number | bigint;
  baseFee: number | bigint;
  remainingAmounts: Array<number | bigint>;
};

export function getSingleSolPaymentInstructionDataEncoder(): Encoder<SingleSolPaymentInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', fixEncoderSize(getBytesEncoder(), 8)],
      ['amount', getU64Encoder()],
      ['baseFee', getU64Encoder()],
      ['remainingAmounts', getArrayEncoder(getU64Encoder())],
    ]),
    (value) => ({ ...value, discriminator: SINGLE_SOL_PAYMENT_DISCRIMINATOR })
  );
}

export function getSingleSolPaymentInstructionDataDecoder(): Decoder<SingleSolPaymentInstructionData> {
  return getStructDecoder([
    ['discriminator', fixDecoderSize(getBytesDecoder(), 8)],
    ['amount', getU64Decoder()],
    ['baseFee', getU64Decoder()],
    ['remainingAmounts', getArrayDecoder(getU64Decoder())],
  ]);
}

export function getSingleSolPaymentInstructionDataCodec(): Codec<
  SingleSolPaymentInstructionDataArgs,
  SingleSolPaymentInstructionData
> {
  return combineCodec(
    getSingleSolPaymentInstructionDataEncoder(),
    getSingleSolPaymentInstructionDataDecoder()
  );
}

export type SingleSolPaymentInput<
  TAccountSender extends string = string,
  TAccountRecipient extends string = string,
  TAccountHelioFeeAccount extends string = string,
  TAccountDaoFeeAccount extends string = string,
  TAccountSystemProgram extends string = string,
> = {
  sender: TransactionSigner<TAccountSender>;
  recipient: Address<TAccountRecipient>;
  helioFeeAccount: Address<TAccountHelioFeeAccount>;
  daoFeeAccount: Address<TAccountDaoFeeAccount>;
  systemProgram?: Address<TAccountSystemProgram>;
  amount: SingleSolPaymentInstructionDataArgs['amount'];
  baseFee: SingleSolPaymentInstructionDataArgs['baseFee'];
  remainingAmounts: SingleSolPaymentInstructionDataArgs['remainingAmounts'];
};

export function getSingleSolPaymentInstruction<
  TAccountSender extends string,
  TAccountRecipient extends string,
  TAccountHelioFeeAccount extends string,
  TAccountDaoFeeAccount extends string,
  TAccountSystemProgram extends string,
  TProgramAddress extends Address = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
>(
  input: SingleSolPaymentInput<
    TAccountSender,
    TAccountRecipient,
    TAccountHelioFeeAccount,
    TAccountDaoFeeAccount,
    TAccountSystemProgram
  >,
  config?: { programAddress?: TProgramAddress }
): SingleSolPaymentInstruction<
  TProgramAddress,
  TAccountSender,
  TAccountRecipient,
  TAccountHelioFeeAccount,
  TAccountDaoFeeAccount,
  TAccountSystemProgram
> {
  // Program address.
  const programAddress =
    config?.programAddress ?? HELIO_PROTOCOL_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    sender: { value: input.sender ?? null, isWritable: true },
    recipient: { value: input.recipient ?? null, isWritable: true },
    helioFeeAccount: { value: input.helioFeeAccount ?? null, isWritable: true },
    daoFeeAccount: { value: input.daoFeeAccount ?? null, isWritable: true },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Resolve default values.
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value =
      '11111111111111111111111111111111' as Address<'11111111111111111111111111111111'>;
  }

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.sender),
      getAccountMeta(accounts.recipient),
      getAccountMeta(accounts.helioFeeAccount),
      getAccountMeta(accounts.daoFeeAccount),
      getAccountMeta(accounts.systemProgram),
    ],
    programAddress,
    data: getSingleSolPaymentInstructionDataEncoder().encode(
      args as SingleSolPaymentInstructionDataArgs
    ),
  } as SingleSolPaymentInstruction<
    TProgramAddress,
    TAccountSender,
    TAccountRecipient,
    TAccountHelioFeeAccount,
    TAccountDaoFeeAccount,
    TAccountSystemProgram
  >;

  return instruction;
}

export type ParsedSingleSolPaymentInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    sender: TAccountMetas[0];
    recipient: TAccountMetas[1];
    helioFeeAccount: TAccountMetas[2];
    daoFeeAccount: TAccountMetas[3];
    systemProgram: TAccountMetas[4];
  };
  data: SingleSolPaymentInstructionData;
};

export function parseSingleSolPaymentInstruction<
  TProgram extends string,
  TAccountMetas extends readonly IAccountMeta[],
>(
  instruction: IInstruction<TProgram> &
    IInstructionWithAccounts<TAccountMetas> &
    IInstructionWithData<Uint8Array>
): ParsedSingleSolPaymentInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 5) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts![accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      sender: getNextAccount(),
      recipient: getNextAccount(),
      helioFeeAccount: getNextAccount(),
      daoFeeAccount: getNextAccount(),
      systemProgram: getNextAccount(),
    },
    data: getSingleSolPaymentInstructionDataDecoder().decode(instruction.data),
  };
}
