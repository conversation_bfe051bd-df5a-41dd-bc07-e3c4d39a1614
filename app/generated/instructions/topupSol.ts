/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  fixDecoderSize,
  fixEncoderSize,
  getBytesDecoder,
  getBytesEncoder,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  transformEncoder,
  type Address,
  type Codec,
  type Decoder,
  type Encoder,
  type IAccountMeta,
  type IInstruction,
  type IInstructionWithAccounts,
  type IInstructionWithData,
  type ReadonlyAccount,
  type ReadonlyUint8Array,
  type WritableAccount,
} from '@solana/kit';
import { HELIO_PROTOCOL_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const TOPUP_SOL_DISCRIMINATOR = new Uint8Array([
  79, 110, 51, 253, 35, 240, 52, 176,
]);

export function getTopupSolDiscriminatorBytes() {
  return fixEncoderSize(getBytesEncoder(), 8).encode(TOPUP_SOL_DISCRIMINATOR);
}

export type TopupSolInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountSender extends string | IAccountMeta<string> = string,
  TAccountSolPaymentAccount extends string | IAccountMeta<string> = string,
  TAccountSystemProgram extends
    | string
    | IAccountMeta<string> = '11111111111111111111111111111111',
  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],
> = IInstruction<TProgram> &
  IInstructionWithData<Uint8Array> &
  IInstructionWithAccounts<
    [
      TAccountSender extends string
        ? WritableAccount<TAccountSender>
        : TAccountSender,
      TAccountSolPaymentAccount extends string
        ? WritableAccount<TAccountSolPaymentAccount>
        : TAccountSolPaymentAccount,
      TAccountSystemProgram extends string
        ? ReadonlyAccount<TAccountSystemProgram>
        : TAccountSystemProgram,
      ...TRemainingAccounts,
    ]
  >;

export type TopupSolInstructionData = {
  discriminator: ReadonlyUint8Array;
  topupAmount: bigint;
};

export type TopupSolInstructionDataArgs = { topupAmount: number | bigint };

export function getTopupSolInstructionDataEncoder(): Encoder<TopupSolInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', fixEncoderSize(getBytesEncoder(), 8)],
      ['topupAmount', getU64Encoder()],
    ]),
    (value) => ({ ...value, discriminator: TOPUP_SOL_DISCRIMINATOR })
  );
}

export function getTopupSolInstructionDataDecoder(): Decoder<TopupSolInstructionData> {
  return getStructDecoder([
    ['discriminator', fixDecoderSize(getBytesDecoder(), 8)],
    ['topupAmount', getU64Decoder()],
  ]);
}

export function getTopupSolInstructionDataCodec(): Codec<
  TopupSolInstructionDataArgs,
  TopupSolInstructionData
> {
  return combineCodec(
    getTopupSolInstructionDataEncoder(),
    getTopupSolInstructionDataDecoder()
  );
}

export type TopupSolInput<
  TAccountSender extends string = string,
  TAccountSolPaymentAccount extends string = string,
  TAccountSystemProgram extends string = string,
> = {
  sender: Address<TAccountSender>;
  solPaymentAccount: Address<TAccountSolPaymentAccount>;
  systemProgram?: Address<TAccountSystemProgram>;
  topupAmount: TopupSolInstructionDataArgs['topupAmount'];
};

export function getTopupSolInstruction<
  TAccountSender extends string,
  TAccountSolPaymentAccount extends string,
  TAccountSystemProgram extends string,
  TProgramAddress extends Address = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
>(
  input: TopupSolInput<
    TAccountSender,
    TAccountSolPaymentAccount,
    TAccountSystemProgram
  >,
  config?: { programAddress?: TProgramAddress }
): TopupSolInstruction<
  TProgramAddress,
  TAccountSender,
  TAccountSolPaymentAccount,
  TAccountSystemProgram
> {
  // Program address.
  const programAddress =
    config?.programAddress ?? HELIO_PROTOCOL_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    sender: { value: input.sender ?? null, isWritable: true },
    solPaymentAccount: {
      value: input.solPaymentAccount ?? null,
      isWritable: true,
    },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Resolve default values.
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value =
      '11111111111111111111111111111111' as Address<'11111111111111111111111111111111'>;
  }

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.sender),
      getAccountMeta(accounts.solPaymentAccount),
      getAccountMeta(accounts.systemProgram),
    ],
    programAddress,
    data: getTopupSolInstructionDataEncoder().encode(
      args as TopupSolInstructionDataArgs
    ),
  } as TopupSolInstruction<
    TProgramAddress,
    TAccountSender,
    TAccountSolPaymentAccount,
    TAccountSystemProgram
  >;

  return instruction;
}

export type ParsedTopupSolInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    sender: TAccountMetas[0];
    solPaymentAccount: TAccountMetas[1];
    systemProgram: TAccountMetas[2];
  };
  data: TopupSolInstructionData;
};

export function parseTopupSolInstruction<
  TProgram extends string,
  TAccountMetas extends readonly IAccountMeta[],
>(
  instruction: IInstruction<TProgram> &
    IInstructionWithAccounts<TAccountMetas> &
    IInstructionWithData<Uint8Array>
): ParsedTopupSolInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 3) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts![accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      sender: getNextAccount(),
      solPaymentAccount: getNextAccount(),
      systemProgram: getNextAccount(),
    },
    data: getTopupSolInstructionDataDecoder().decode(instruction.data),
  };
}
