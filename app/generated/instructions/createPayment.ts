/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  fixDecoderSize,
  fixEncoderSize,
  getBooleanDecoder,
  getBooleanEncoder,
  getBytesDecoder,
  getBytesEncoder,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  getU8Decoder,
  getU8Encoder,
  transformEncoder,
  type Address,
  type Codec,
  type Decoder,
  type Encoder,
  type IAccountMeta,
  type IAccountSignerMeta,
  type IInstruction,
  type IInstructionWithAccounts,
  type IInstructionWithData,
  type ReadonlyAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
  type WritableSignerAccount,
} from '@solana/kit';
import { HELIO_PROTOCOL_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const CREATE_PAYMENT_DISCRIMINATOR = new Uint8Array([
  28, 81, 85, 253, 7, 223, 154, 42,
]);

export function getCreatePaymentDiscriminatorBytes() {
  return fixEncoderSize(getBytesEncoder(), 8).encode(
    CREATE_PAYMENT_DISCRIMINATOR
  );
}

export type CreatePaymentInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountSender extends string | IAccountMeta<string> = string,
  TAccountSenderTokenAccount extends string | IAccountMeta<string> = string,
  TAccountPaymentAccount extends string | IAccountMeta<string> = string,
  TAccountPaymentTokenAccount extends string | IAccountMeta<string> = string,
  TAccountRecipientTokenAccount extends string | IAccountMeta<string> = string,
  TAccountHelioFeeTokenAccount extends string | IAccountMeta<string> = string,
  TAccountDaoFeeTokenAccount extends string | IAccountMeta<string> = string,
  TAccountRecipient extends string | IAccountMeta<string> = string,
  TAccountHelioFeeAccount extends string | IAccountMeta<string> = string,
  TAccountDaoFeeAccount extends string | IAccountMeta<string> = string,
  TAccountMint extends string | IAccountMeta<string> = string,
  TAccountTokenProgram extends
    | string
    | IAccountMeta<string> = 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
  TAccountAssociatedTokenProgram extends string | IAccountMeta<string> = string,
  TAccountSystemProgram extends
    | string
    | IAccountMeta<string> = '11111111111111111111111111111111',
  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],
> = IInstruction<TProgram> &
  IInstructionWithData<Uint8Array> &
  IInstructionWithAccounts<
    [
      TAccountSender extends string
        ? WritableSignerAccount<TAccountSender> &
            IAccountSignerMeta<TAccountSender>
        : TAccountSender,
      TAccountSenderTokenAccount extends string
        ? WritableAccount<TAccountSenderTokenAccount>
        : TAccountSenderTokenAccount,
      TAccountPaymentAccount extends string
        ? WritableSignerAccount<TAccountPaymentAccount> &
            IAccountSignerMeta<TAccountPaymentAccount>
        : TAccountPaymentAccount,
      TAccountPaymentTokenAccount extends string
        ? WritableAccount<TAccountPaymentTokenAccount>
        : TAccountPaymentTokenAccount,
      TAccountRecipientTokenAccount extends string
        ? WritableAccount<TAccountRecipientTokenAccount>
        : TAccountRecipientTokenAccount,
      TAccountHelioFeeTokenAccount extends string
        ? WritableAccount<TAccountHelioFeeTokenAccount>
        : TAccountHelioFeeTokenAccount,
      TAccountDaoFeeTokenAccount extends string
        ? WritableAccount<TAccountDaoFeeTokenAccount>
        : TAccountDaoFeeTokenAccount,
      TAccountRecipient extends string
        ? ReadonlyAccount<TAccountRecipient>
        : TAccountRecipient,
      TAccountHelioFeeAccount extends string
        ? ReadonlyAccount<TAccountHelioFeeAccount>
        : TAccountHelioFeeAccount,
      TAccountDaoFeeAccount extends string
        ? ReadonlyAccount<TAccountDaoFeeAccount>
        : TAccountDaoFeeAccount,
      TAccountMint extends string
        ? ReadonlyAccount<TAccountMint>
        : TAccountMint,
      TAccountTokenProgram extends string
        ? ReadonlyAccount<TAccountTokenProgram>
        : TAccountTokenProgram,
      TAccountAssociatedTokenProgram extends string
        ? ReadonlyAccount<TAccountAssociatedTokenProgram>
        : TAccountAssociatedTokenProgram,
      TAccountSystemProgram extends string
        ? ReadonlyAccount<TAccountSystemProgram>
        : TAccountSystemProgram,
      ...TRemainingAccounts,
    ]
  >;

export type CreatePaymentInstructionData = {
  discriminator: ReadonlyUint8Array;
  amount: bigint;
  startAt: bigint;
  endAt: bigint;
  interval: bigint;
  bump: number;
  payFees: boolean;
};

export type CreatePaymentInstructionDataArgs = {
  amount: number | bigint;
  startAt: number | bigint;
  endAt: number | bigint;
  interval: number | bigint;
  bump: number;
  payFees: boolean;
};

export function getCreatePaymentInstructionDataEncoder(): Encoder<CreatePaymentInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', fixEncoderSize(getBytesEncoder(), 8)],
      ['amount', getU64Encoder()],
      ['startAt', getU64Encoder()],
      ['endAt', getU64Encoder()],
      ['interval', getU64Encoder()],
      ['bump', getU8Encoder()],
      ['payFees', getBooleanEncoder()],
    ]),
    (value) => ({ ...value, discriminator: CREATE_PAYMENT_DISCRIMINATOR })
  );
}

export function getCreatePaymentInstructionDataDecoder(): Decoder<CreatePaymentInstructionData> {
  return getStructDecoder([
    ['discriminator', fixDecoderSize(getBytesDecoder(), 8)],
    ['amount', getU64Decoder()],
    ['startAt', getU64Decoder()],
    ['endAt', getU64Decoder()],
    ['interval', getU64Decoder()],
    ['bump', getU8Decoder()],
    ['payFees', getBooleanDecoder()],
  ]);
}

export function getCreatePaymentInstructionDataCodec(): Codec<
  CreatePaymentInstructionDataArgs,
  CreatePaymentInstructionData
> {
  return combineCodec(
    getCreatePaymentInstructionDataEncoder(),
    getCreatePaymentInstructionDataDecoder()
  );
}

export type CreatePaymentInput<
  TAccountSender extends string = string,
  TAccountSenderTokenAccount extends string = string,
  TAccountPaymentAccount extends string = string,
  TAccountPaymentTokenAccount extends string = string,
  TAccountRecipientTokenAccount extends string = string,
  TAccountHelioFeeTokenAccount extends string = string,
  TAccountDaoFeeTokenAccount extends string = string,
  TAccountRecipient extends string = string,
  TAccountHelioFeeAccount extends string = string,
  TAccountDaoFeeAccount extends string = string,
  TAccountMint extends string = string,
  TAccountTokenProgram extends string = string,
  TAccountAssociatedTokenProgram extends string = string,
  TAccountSystemProgram extends string = string,
> = {
  sender: TransactionSigner<TAccountSender>;
  senderTokenAccount: Address<TAccountSenderTokenAccount>;
  paymentAccount: TransactionSigner<TAccountPaymentAccount>;
  paymentTokenAccount: Address<TAccountPaymentTokenAccount>;
  recipientTokenAccount: Address<TAccountRecipientTokenAccount>;
  helioFeeTokenAccount: Address<TAccountHelioFeeTokenAccount>;
  daoFeeTokenAccount: Address<TAccountDaoFeeTokenAccount>;
  recipient: Address<TAccountRecipient>;
  helioFeeAccount: Address<TAccountHelioFeeAccount>;
  daoFeeAccount: Address<TAccountDaoFeeAccount>;
  mint: Address<TAccountMint>;
  tokenProgram?: Address<TAccountTokenProgram>;
  associatedTokenProgram: Address<TAccountAssociatedTokenProgram>;
  systemProgram?: Address<TAccountSystemProgram>;
  amount: CreatePaymentInstructionDataArgs['amount'];
  startAt: CreatePaymentInstructionDataArgs['startAt'];
  endAt: CreatePaymentInstructionDataArgs['endAt'];
  interval: CreatePaymentInstructionDataArgs['interval'];
  bump: CreatePaymentInstructionDataArgs['bump'];
  payFees: CreatePaymentInstructionDataArgs['payFees'];
};

export function getCreatePaymentInstruction<
  TAccountSender extends string,
  TAccountSenderTokenAccount extends string,
  TAccountPaymentAccount extends string,
  TAccountPaymentTokenAccount extends string,
  TAccountRecipientTokenAccount extends string,
  TAccountHelioFeeTokenAccount extends string,
  TAccountDaoFeeTokenAccount extends string,
  TAccountRecipient extends string,
  TAccountHelioFeeAccount extends string,
  TAccountDaoFeeAccount extends string,
  TAccountMint extends string,
  TAccountTokenProgram extends string,
  TAccountAssociatedTokenProgram extends string,
  TAccountSystemProgram extends string,
  TProgramAddress extends Address = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
>(
  input: CreatePaymentInput<
    TAccountSender,
    TAccountSenderTokenAccount,
    TAccountPaymentAccount,
    TAccountPaymentTokenAccount,
    TAccountRecipientTokenAccount,
    TAccountHelioFeeTokenAccount,
    TAccountDaoFeeTokenAccount,
    TAccountRecipient,
    TAccountHelioFeeAccount,
    TAccountDaoFeeAccount,
    TAccountMint,
    TAccountTokenProgram,
    TAccountAssociatedTokenProgram,
    TAccountSystemProgram
  >,
  config?: { programAddress?: TProgramAddress }
): CreatePaymentInstruction<
  TProgramAddress,
  TAccountSender,
  TAccountSenderTokenAccount,
  TAccountPaymentAccount,
  TAccountPaymentTokenAccount,
  TAccountRecipientTokenAccount,
  TAccountHelioFeeTokenAccount,
  TAccountDaoFeeTokenAccount,
  TAccountRecipient,
  TAccountHelioFeeAccount,
  TAccountDaoFeeAccount,
  TAccountMint,
  TAccountTokenProgram,
  TAccountAssociatedTokenProgram,
  TAccountSystemProgram
> {
  // Program address.
  const programAddress =
    config?.programAddress ?? HELIO_PROTOCOL_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    sender: { value: input.sender ?? null, isWritable: true },
    senderTokenAccount: {
      value: input.senderTokenAccount ?? null,
      isWritable: true,
    },
    paymentAccount: { value: input.paymentAccount ?? null, isWritable: true },
    paymentTokenAccount: {
      value: input.paymentTokenAccount ?? null,
      isWritable: true,
    },
    recipientTokenAccount: {
      value: input.recipientTokenAccount ?? null,
      isWritable: true,
    },
    helioFeeTokenAccount: {
      value: input.helioFeeTokenAccount ?? null,
      isWritable: true,
    },
    daoFeeTokenAccount: {
      value: input.daoFeeTokenAccount ?? null,
      isWritable: true,
    },
    recipient: { value: input.recipient ?? null, isWritable: false },
    helioFeeAccount: {
      value: input.helioFeeAccount ?? null,
      isWritable: false,
    },
    daoFeeAccount: { value: input.daoFeeAccount ?? null, isWritable: false },
    mint: { value: input.mint ?? null, isWritable: false },
    tokenProgram: { value: input.tokenProgram ?? null, isWritable: false },
    associatedTokenProgram: {
      value: input.associatedTokenProgram ?? null,
      isWritable: false,
    },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Resolve default values.
  if (!accounts.tokenProgram.value) {
    accounts.tokenProgram.value =
      'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' as Address<'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'>;
  }
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value =
      '11111111111111111111111111111111' as Address<'11111111111111111111111111111111'>;
  }

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.sender),
      getAccountMeta(accounts.senderTokenAccount),
      getAccountMeta(accounts.paymentAccount),
      getAccountMeta(accounts.paymentTokenAccount),
      getAccountMeta(accounts.recipientTokenAccount),
      getAccountMeta(accounts.helioFeeTokenAccount),
      getAccountMeta(accounts.daoFeeTokenAccount),
      getAccountMeta(accounts.recipient),
      getAccountMeta(accounts.helioFeeAccount),
      getAccountMeta(accounts.daoFeeAccount),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.tokenProgram),
      getAccountMeta(accounts.associatedTokenProgram),
      getAccountMeta(accounts.systemProgram),
    ],
    programAddress,
    data: getCreatePaymentInstructionDataEncoder().encode(
      args as CreatePaymentInstructionDataArgs
    ),
  } as CreatePaymentInstruction<
    TProgramAddress,
    TAccountSender,
    TAccountSenderTokenAccount,
    TAccountPaymentAccount,
    TAccountPaymentTokenAccount,
    TAccountRecipientTokenAccount,
    TAccountHelioFeeTokenAccount,
    TAccountDaoFeeTokenAccount,
    TAccountRecipient,
    TAccountHelioFeeAccount,
    TAccountDaoFeeAccount,
    TAccountMint,
    TAccountTokenProgram,
    TAccountAssociatedTokenProgram,
    TAccountSystemProgram
  >;

  return instruction;
}

export type ParsedCreatePaymentInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    sender: TAccountMetas[0];
    senderTokenAccount: TAccountMetas[1];
    paymentAccount: TAccountMetas[2];
    paymentTokenAccount: TAccountMetas[3];
    recipientTokenAccount: TAccountMetas[4];
    helioFeeTokenAccount: TAccountMetas[5];
    daoFeeTokenAccount: TAccountMetas[6];
    recipient: TAccountMetas[7];
    helioFeeAccount: TAccountMetas[8];
    daoFeeAccount: TAccountMetas[9];
    mint: TAccountMetas[10];
    tokenProgram: TAccountMetas[11];
    associatedTokenProgram: TAccountMetas[12];
    systemProgram: TAccountMetas[13];
  };
  data: CreatePaymentInstructionData;
};

export function parseCreatePaymentInstruction<
  TProgram extends string,
  TAccountMetas extends readonly IAccountMeta[],
>(
  instruction: IInstruction<TProgram> &
    IInstructionWithAccounts<TAccountMetas> &
    IInstructionWithData<Uint8Array>
): ParsedCreatePaymentInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 14) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts![accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      sender: getNextAccount(),
      senderTokenAccount: getNextAccount(),
      paymentAccount: getNextAccount(),
      paymentTokenAccount: getNextAccount(),
      recipientTokenAccount: getNextAccount(),
      helioFeeTokenAccount: getNextAccount(),
      daoFeeTokenAccount: getNextAccount(),
      recipient: getNextAccount(),
      helioFeeAccount: getNextAccount(),
      daoFeeAccount: getNextAccount(),
      mint: getNextAccount(),
      tokenProgram: getNextAccount(),
      associatedTokenProgram: getNextAccount(),
      systemProgram: getNextAccount(),
    },
    data: getCreatePaymentInstructionDataDecoder().decode(instruction.data),
  };
}
