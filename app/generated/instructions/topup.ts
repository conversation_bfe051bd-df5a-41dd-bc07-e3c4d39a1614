/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  fixDecoderSize,
  fixEncoderSize,
  getBytesDecoder,
  getBytesEncoder,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  transformEncoder,
  type Address,
  type Codec,
  type Decoder,
  type Encoder,
  type IAccountMeta,
  type IInstruction,
  type IInstructionWithAccounts,
  type IInstructionWithData,
  type ReadonlyAccount,
  type ReadonlyUint8Array,
  type WritableAccount,
} from '@solana/kit';
import { HELIO_PROTOCOL_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const TOPUP_DISCRIMINATOR = new Uint8Array([
  126, 42, 49, 78, 225, 151, 99, 77,
]);

export function getTopupDiscriminatorBytes() {
  return fixEncoderSize(getBytesEncoder(), 8).encode(TOPUP_DISCRIMINATOR);
}

export type TopupInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountSender extends string | IAccountMeta<string> = string,
  TAccountSenderTokenAccount extends string | IAccountMeta<string> = string,
  TAccountPaymentAccount extends string | IAccountMeta<string> = string,
  TAccountPaymentTokenAccount extends string | IAccountMeta<string> = string,
  TAccountPdaSigner extends string | IAccountMeta<string> = string,
  TAccountMint extends string | IAccountMeta<string> = string,
  TAccountTokenProgram extends
    | string
    | IAccountMeta<string> = 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],
> = IInstruction<TProgram> &
  IInstructionWithData<Uint8Array> &
  IInstructionWithAccounts<
    [
      TAccountSender extends string
        ? WritableAccount<TAccountSender>
        : TAccountSender,
      TAccountSenderTokenAccount extends string
        ? WritableAccount<TAccountSenderTokenAccount>
        : TAccountSenderTokenAccount,
      TAccountPaymentAccount extends string
        ? WritableAccount<TAccountPaymentAccount>
        : TAccountPaymentAccount,
      TAccountPaymentTokenAccount extends string
        ? WritableAccount<TAccountPaymentTokenAccount>
        : TAccountPaymentTokenAccount,
      TAccountPdaSigner extends string
        ? ReadonlyAccount<TAccountPdaSigner>
        : TAccountPdaSigner,
      TAccountMint extends string
        ? ReadonlyAccount<TAccountMint>
        : TAccountMint,
      TAccountTokenProgram extends string
        ? ReadonlyAccount<TAccountTokenProgram>
        : TAccountTokenProgram,
      ...TRemainingAccounts,
    ]
  >;

export type TopupInstructionData = {
  discriminator: ReadonlyUint8Array;
  topupAmount: bigint;
};

export type TopupInstructionDataArgs = { topupAmount: number | bigint };

export function getTopupInstructionDataEncoder(): Encoder<TopupInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', fixEncoderSize(getBytesEncoder(), 8)],
      ['topupAmount', getU64Encoder()],
    ]),
    (value) => ({ ...value, discriminator: TOPUP_DISCRIMINATOR })
  );
}

export function getTopupInstructionDataDecoder(): Decoder<TopupInstructionData> {
  return getStructDecoder([
    ['discriminator', fixDecoderSize(getBytesDecoder(), 8)],
    ['topupAmount', getU64Decoder()],
  ]);
}

export function getTopupInstructionDataCodec(): Codec<
  TopupInstructionDataArgs,
  TopupInstructionData
> {
  return combineCodec(
    getTopupInstructionDataEncoder(),
    getTopupInstructionDataDecoder()
  );
}

export type TopupInput<
  TAccountSender extends string = string,
  TAccountSenderTokenAccount extends string = string,
  TAccountPaymentAccount extends string = string,
  TAccountPaymentTokenAccount extends string = string,
  TAccountPdaSigner extends string = string,
  TAccountMint extends string = string,
  TAccountTokenProgram extends string = string,
> = {
  sender: Address<TAccountSender>;
  senderTokenAccount: Address<TAccountSenderTokenAccount>;
  paymentAccount: Address<TAccountPaymentAccount>;
  paymentTokenAccount: Address<TAccountPaymentTokenAccount>;
  pdaSigner: Address<TAccountPdaSigner>;
  mint: Address<TAccountMint>;
  tokenProgram?: Address<TAccountTokenProgram>;
  topupAmount: TopupInstructionDataArgs['topupAmount'];
};

export function getTopupInstruction<
  TAccountSender extends string,
  TAccountSenderTokenAccount extends string,
  TAccountPaymentAccount extends string,
  TAccountPaymentTokenAccount extends string,
  TAccountPdaSigner extends string,
  TAccountMint extends string,
  TAccountTokenProgram extends string,
  TProgramAddress extends Address = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
>(
  input: TopupInput<
    TAccountSender,
    TAccountSenderTokenAccount,
    TAccountPaymentAccount,
    TAccountPaymentTokenAccount,
    TAccountPdaSigner,
    TAccountMint,
    TAccountTokenProgram
  >,
  config?: { programAddress?: TProgramAddress }
): TopupInstruction<
  TProgramAddress,
  TAccountSender,
  TAccountSenderTokenAccount,
  TAccountPaymentAccount,
  TAccountPaymentTokenAccount,
  TAccountPdaSigner,
  TAccountMint,
  TAccountTokenProgram
> {
  // Program address.
  const programAddress =
    config?.programAddress ?? HELIO_PROTOCOL_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    sender: { value: input.sender ?? null, isWritable: true },
    senderTokenAccount: {
      value: input.senderTokenAccount ?? null,
      isWritable: true,
    },
    paymentAccount: { value: input.paymentAccount ?? null, isWritable: true },
    paymentTokenAccount: {
      value: input.paymentTokenAccount ?? null,
      isWritable: true,
    },
    pdaSigner: { value: input.pdaSigner ?? null, isWritable: false },
    mint: { value: input.mint ?? null, isWritable: false },
    tokenProgram: { value: input.tokenProgram ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Resolve default values.
  if (!accounts.tokenProgram.value) {
    accounts.tokenProgram.value =
      'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' as Address<'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'>;
  }

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.sender),
      getAccountMeta(accounts.senderTokenAccount),
      getAccountMeta(accounts.paymentAccount),
      getAccountMeta(accounts.paymentTokenAccount),
      getAccountMeta(accounts.pdaSigner),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.tokenProgram),
    ],
    programAddress,
    data: getTopupInstructionDataEncoder().encode(
      args as TopupInstructionDataArgs
    ),
  } as TopupInstruction<
    TProgramAddress,
    TAccountSender,
    TAccountSenderTokenAccount,
    TAccountPaymentAccount,
    TAccountPaymentTokenAccount,
    TAccountPdaSigner,
    TAccountMint,
    TAccountTokenProgram
  >;

  return instruction;
}

export type ParsedTopupInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    sender: TAccountMetas[0];
    senderTokenAccount: TAccountMetas[1];
    paymentAccount: TAccountMetas[2];
    paymentTokenAccount: TAccountMetas[3];
    pdaSigner: TAccountMetas[4];
    mint: TAccountMetas[5];
    tokenProgram: TAccountMetas[6];
  };
  data: TopupInstructionData;
};

export function parseTopupInstruction<
  TProgram extends string,
  TAccountMetas extends readonly IAccountMeta[],
>(
  instruction: IInstruction<TProgram> &
    IInstructionWithAccounts<TAccountMetas> &
    IInstructionWithData<Uint8Array>
): ParsedTopupInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 7) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts![accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      sender: getNextAccount(),
      senderTokenAccount: getNextAccount(),
      paymentAccount: getNextAccount(),
      paymentTokenAccount: getNextAccount(),
      pdaSigner: getNextAccount(),
      mint: getNextAccount(),
      tokenProgram: getNextAccount(),
    },
    data: getTopupInstructionDataDecoder().decode(instruction.data),
  };
}
