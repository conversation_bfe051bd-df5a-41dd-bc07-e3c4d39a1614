/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  fixDecoderSize,
  fixEncoderSize,
  getBytesDecoder,
  getBytesEncoder,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  transformEncoder,
  type Address,
  type Codec,
  type Decoder,
  type Encoder,
  type IAccountMeta,
  type IAccountSignerMeta,
  type IInstruction,
  type IInstructionWithAccounts,
  type IInstructionWithData,
  type ReadonlyAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
  type WritableSignerAccount,
} from '@solana/kit';
import { HELIO_PROTOCOL_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const WITHDRAW_DISCRIMINATOR = new Uint8Array([
  183, 18, 70, 156, 148, 109, 161, 34,
]);

export function getWithdrawDiscriminatorBytes() {
  return fixEncoderSize(getBytesEncoder(), 8).encode(WITHDRAW_DISCRIMINATOR);
}

export type WithdrawInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountSigner extends string | IAccountMeta<string> = string,
  TAccountRecipient extends string | IAccountMeta<string> = string,
  TAccountRecipientTokenAccount extends string | IAccountMeta<string> = string,
  TAccountPaymentAccount extends string | IAccountMeta<string> = string,
  TAccountPaymentTokenAccount extends string | IAccountMeta<string> = string,
  TAccountHelioFeeTokenAccount extends string | IAccountMeta<string> = string,
  TAccountDaoFeeTokenAccount extends string | IAccountMeta<string> = string,
  TAccountHelioFeeAccount extends string | IAccountMeta<string> = string,
  TAccountDaoFeeAccount extends string | IAccountMeta<string> = string,
  TAccountPdaSigner extends string | IAccountMeta<string> = string,
  TAccountMint extends string | IAccountMeta<string> = string,
  TAccountTokenProgram extends
    | string
    | IAccountMeta<string> = 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
  TAccountSystemProgram extends
    | string
    | IAccountMeta<string> = '11111111111111111111111111111111',
  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],
> = IInstruction<TProgram> &
  IInstructionWithData<Uint8Array> &
  IInstructionWithAccounts<
    [
      TAccountSigner extends string
        ? WritableSignerAccount<TAccountSigner> &
            IAccountSignerMeta<TAccountSigner>
        : TAccountSigner,
      TAccountRecipient extends string
        ? WritableAccount<TAccountRecipient>
        : TAccountRecipient,
      TAccountRecipientTokenAccount extends string
        ? WritableAccount<TAccountRecipientTokenAccount>
        : TAccountRecipientTokenAccount,
      TAccountPaymentAccount extends string
        ? WritableAccount<TAccountPaymentAccount>
        : TAccountPaymentAccount,
      TAccountPaymentTokenAccount extends string
        ? WritableAccount<TAccountPaymentTokenAccount>
        : TAccountPaymentTokenAccount,
      TAccountHelioFeeTokenAccount extends string
        ? WritableAccount<TAccountHelioFeeTokenAccount>
        : TAccountHelioFeeTokenAccount,
      TAccountDaoFeeTokenAccount extends string
        ? WritableAccount<TAccountDaoFeeTokenAccount>
        : TAccountDaoFeeTokenAccount,
      TAccountHelioFeeAccount extends string
        ? ReadonlyAccount<TAccountHelioFeeAccount>
        : TAccountHelioFeeAccount,
      TAccountDaoFeeAccount extends string
        ? ReadonlyAccount<TAccountDaoFeeAccount>
        : TAccountDaoFeeAccount,
      TAccountPdaSigner extends string
        ? ReadonlyAccount<TAccountPdaSigner>
        : TAccountPdaSigner,
      TAccountMint extends string
        ? ReadonlyAccount<TAccountMint>
        : TAccountMint,
      TAccountTokenProgram extends string
        ? ReadonlyAccount<TAccountTokenProgram>
        : TAccountTokenProgram,
      TAccountSystemProgram extends string
        ? ReadonlyAccount<TAccountSystemProgram>
        : TAccountSystemProgram,
      ...TRemainingAccounts,
    ]
  >;

export type WithdrawInstructionData = {
  discriminator: ReadonlyUint8Array;
  baseFee: bigint;
};

export type WithdrawInstructionDataArgs = { baseFee: number | bigint };

export function getWithdrawInstructionDataEncoder(): Encoder<WithdrawInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', fixEncoderSize(getBytesEncoder(), 8)],
      ['baseFee', getU64Encoder()],
    ]),
    (value) => ({ ...value, discriminator: WITHDRAW_DISCRIMINATOR })
  );
}

export function getWithdrawInstructionDataDecoder(): Decoder<WithdrawInstructionData> {
  return getStructDecoder([
    ['discriminator', fixDecoderSize(getBytesDecoder(), 8)],
    ['baseFee', getU64Decoder()],
  ]);
}

export function getWithdrawInstructionDataCodec(): Codec<
  WithdrawInstructionDataArgs,
  WithdrawInstructionData
> {
  return combineCodec(
    getWithdrawInstructionDataEncoder(),
    getWithdrawInstructionDataDecoder()
  );
}

export type WithdrawInput<
  TAccountSigner extends string = string,
  TAccountRecipient extends string = string,
  TAccountRecipientTokenAccount extends string = string,
  TAccountPaymentAccount extends string = string,
  TAccountPaymentTokenAccount extends string = string,
  TAccountHelioFeeTokenAccount extends string = string,
  TAccountDaoFeeTokenAccount extends string = string,
  TAccountHelioFeeAccount extends string = string,
  TAccountDaoFeeAccount extends string = string,
  TAccountPdaSigner extends string = string,
  TAccountMint extends string = string,
  TAccountTokenProgram extends string = string,
  TAccountSystemProgram extends string = string,
> = {
  signer: TransactionSigner<TAccountSigner>;
  recipient: Address<TAccountRecipient>;
  recipientTokenAccount: Address<TAccountRecipientTokenAccount>;
  paymentAccount: Address<TAccountPaymentAccount>;
  paymentTokenAccount: Address<TAccountPaymentTokenAccount>;
  helioFeeTokenAccount: Address<TAccountHelioFeeTokenAccount>;
  daoFeeTokenAccount: Address<TAccountDaoFeeTokenAccount>;
  helioFeeAccount: Address<TAccountHelioFeeAccount>;
  daoFeeAccount: Address<TAccountDaoFeeAccount>;
  pdaSigner: Address<TAccountPdaSigner>;
  mint: Address<TAccountMint>;
  tokenProgram?: Address<TAccountTokenProgram>;
  systemProgram?: Address<TAccountSystemProgram>;
  baseFee: WithdrawInstructionDataArgs['baseFee'];
};

export function getWithdrawInstruction<
  TAccountSigner extends string,
  TAccountRecipient extends string,
  TAccountRecipientTokenAccount extends string,
  TAccountPaymentAccount extends string,
  TAccountPaymentTokenAccount extends string,
  TAccountHelioFeeTokenAccount extends string,
  TAccountDaoFeeTokenAccount extends string,
  TAccountHelioFeeAccount extends string,
  TAccountDaoFeeAccount extends string,
  TAccountPdaSigner extends string,
  TAccountMint extends string,
  TAccountTokenProgram extends string,
  TAccountSystemProgram extends string,
  TProgramAddress extends Address = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
>(
  input: WithdrawInput<
    TAccountSigner,
    TAccountRecipient,
    TAccountRecipientTokenAccount,
    TAccountPaymentAccount,
    TAccountPaymentTokenAccount,
    TAccountHelioFeeTokenAccount,
    TAccountDaoFeeTokenAccount,
    TAccountHelioFeeAccount,
    TAccountDaoFeeAccount,
    TAccountPdaSigner,
    TAccountMint,
    TAccountTokenProgram,
    TAccountSystemProgram
  >,
  config?: { programAddress?: TProgramAddress }
): WithdrawInstruction<
  TProgramAddress,
  TAccountSigner,
  TAccountRecipient,
  TAccountRecipientTokenAccount,
  TAccountPaymentAccount,
  TAccountPaymentTokenAccount,
  TAccountHelioFeeTokenAccount,
  TAccountDaoFeeTokenAccount,
  TAccountHelioFeeAccount,
  TAccountDaoFeeAccount,
  TAccountPdaSigner,
  TAccountMint,
  TAccountTokenProgram,
  TAccountSystemProgram
> {
  // Program address.
  const programAddress =
    config?.programAddress ?? HELIO_PROTOCOL_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    signer: { value: input.signer ?? null, isWritable: true },
    recipient: { value: input.recipient ?? null, isWritable: true },
    recipientTokenAccount: {
      value: input.recipientTokenAccount ?? null,
      isWritable: true,
    },
    paymentAccount: { value: input.paymentAccount ?? null, isWritable: true },
    paymentTokenAccount: {
      value: input.paymentTokenAccount ?? null,
      isWritable: true,
    },
    helioFeeTokenAccount: {
      value: input.helioFeeTokenAccount ?? null,
      isWritable: true,
    },
    daoFeeTokenAccount: {
      value: input.daoFeeTokenAccount ?? null,
      isWritable: true,
    },
    helioFeeAccount: {
      value: input.helioFeeAccount ?? null,
      isWritable: false,
    },
    daoFeeAccount: { value: input.daoFeeAccount ?? null, isWritable: false },
    pdaSigner: { value: input.pdaSigner ?? null, isWritable: false },
    mint: { value: input.mint ?? null, isWritable: false },
    tokenProgram: { value: input.tokenProgram ?? null, isWritable: false },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Resolve default values.
  if (!accounts.tokenProgram.value) {
    accounts.tokenProgram.value =
      'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' as Address<'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'>;
  }
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value =
      '11111111111111111111111111111111' as Address<'11111111111111111111111111111111'>;
  }

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.signer),
      getAccountMeta(accounts.recipient),
      getAccountMeta(accounts.recipientTokenAccount),
      getAccountMeta(accounts.paymentAccount),
      getAccountMeta(accounts.paymentTokenAccount),
      getAccountMeta(accounts.helioFeeTokenAccount),
      getAccountMeta(accounts.daoFeeTokenAccount),
      getAccountMeta(accounts.helioFeeAccount),
      getAccountMeta(accounts.daoFeeAccount),
      getAccountMeta(accounts.pdaSigner),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.tokenProgram),
      getAccountMeta(accounts.systemProgram),
    ],
    programAddress,
    data: getWithdrawInstructionDataEncoder().encode(
      args as WithdrawInstructionDataArgs
    ),
  } as WithdrawInstruction<
    TProgramAddress,
    TAccountSigner,
    TAccountRecipient,
    TAccountRecipientTokenAccount,
    TAccountPaymentAccount,
    TAccountPaymentTokenAccount,
    TAccountHelioFeeTokenAccount,
    TAccountDaoFeeTokenAccount,
    TAccountHelioFeeAccount,
    TAccountDaoFeeAccount,
    TAccountPdaSigner,
    TAccountMint,
    TAccountTokenProgram,
    TAccountSystemProgram
  >;

  return instruction;
}

export type ParsedWithdrawInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    signer: TAccountMetas[0];
    recipient: TAccountMetas[1];
    recipientTokenAccount: TAccountMetas[2];
    paymentAccount: TAccountMetas[3];
    paymentTokenAccount: TAccountMetas[4];
    helioFeeTokenAccount: TAccountMetas[5];
    daoFeeTokenAccount: TAccountMetas[6];
    helioFeeAccount: TAccountMetas[7];
    daoFeeAccount: TAccountMetas[8];
    pdaSigner: TAccountMetas[9];
    mint: TAccountMetas[10];
    tokenProgram: TAccountMetas[11];
    systemProgram: TAccountMetas[12];
  };
  data: WithdrawInstructionData;
};

export function parseWithdrawInstruction<
  TProgram extends string,
  TAccountMetas extends readonly IAccountMeta[],
>(
  instruction: IInstruction<TProgram> &
    IInstructionWithAccounts<TAccountMetas> &
    IInstructionWithData<Uint8Array>
): ParsedWithdrawInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 13) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts![accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      signer: getNextAccount(),
      recipient: getNextAccount(),
      recipientTokenAccount: getNextAccount(),
      paymentAccount: getNextAccount(),
      paymentTokenAccount: getNextAccount(),
      helioFeeTokenAccount: getNextAccount(),
      daoFeeTokenAccount: getNextAccount(),
      helioFeeAccount: getNextAccount(),
      daoFeeAccount: getNextAccount(),
      pdaSigner: getNextAccount(),
      mint: getNextAccount(),
      tokenProgram: getNextAccount(),
      systemProgram: getNextAccount(),
    },
    data: getWithdrawInstructionDataDecoder().decode(instruction.data),
  };
}
