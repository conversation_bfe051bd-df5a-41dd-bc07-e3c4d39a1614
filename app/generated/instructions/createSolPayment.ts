/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  fixDecoderSize,
  fixEncoderSize,
  getBooleanDecoder,
  getBooleanEncoder,
  getBytesDecoder,
  getBytesEncoder,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  transformEncoder,
  type Address,
  type Codec,
  type Decoder,
  type Encoder,
  type IAccountMeta,
  type IAccountSignerMeta,
  type IInstruction,
  type IInstructionWithAccounts,
  type IInstructionWithData,
  type ReadonlyAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableSignerAccount,
} from '@solana/kit';
import { HELIO_PROTOCOL_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const CREATE_SOL_PAYMENT_DISCRIMINATOR = new Uint8Array([
  205, 117, 243, 209, 116, 76, 231, 185,
]);

export function getCreateSolPaymentDiscriminatorBytes() {
  return fixEncoderSize(getBytesEncoder(), 8).encode(
    CREATE_SOL_PAYMENT_DISCRIMINATOR
  );
}

export type CreateSolPaymentInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountSender extends string | IAccountMeta<string> = string,
  TAccountRecipient extends string | IAccountMeta<string> = string,
  TAccountSolPaymentAccount extends string | IAccountMeta<string> = string,
  TAccountSystemProgram extends
    | string
    | IAccountMeta<string> = '11111111111111111111111111111111',
  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],
> = IInstruction<TProgram> &
  IInstructionWithData<Uint8Array> &
  IInstructionWithAccounts<
    [
      TAccountSender extends string
        ? WritableSignerAccount<TAccountSender> &
            IAccountSignerMeta<TAccountSender>
        : TAccountSender,
      TAccountRecipient extends string
        ? ReadonlyAccount<TAccountRecipient>
        : TAccountRecipient,
      TAccountSolPaymentAccount extends string
        ? WritableSignerAccount<TAccountSolPaymentAccount> &
            IAccountSignerMeta<TAccountSolPaymentAccount>
        : TAccountSolPaymentAccount,
      TAccountSystemProgram extends string
        ? ReadonlyAccount<TAccountSystemProgram>
        : TAccountSystemProgram,
      ...TRemainingAccounts,
    ]
  >;

export type CreateSolPaymentInstructionData = {
  discriminator: ReadonlyUint8Array;
  amount: bigint;
  startAt: bigint;
  endAt: bigint;
  interval: bigint;
  payFees: boolean;
};

export type CreateSolPaymentInstructionDataArgs = {
  amount: number | bigint;
  startAt: number | bigint;
  endAt: number | bigint;
  interval: number | bigint;
  payFees: boolean;
};

export function getCreateSolPaymentInstructionDataEncoder(): Encoder<CreateSolPaymentInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', fixEncoderSize(getBytesEncoder(), 8)],
      ['amount', getU64Encoder()],
      ['startAt', getU64Encoder()],
      ['endAt', getU64Encoder()],
      ['interval', getU64Encoder()],
      ['payFees', getBooleanEncoder()],
    ]),
    (value) => ({ ...value, discriminator: CREATE_SOL_PAYMENT_DISCRIMINATOR })
  );
}

export function getCreateSolPaymentInstructionDataDecoder(): Decoder<CreateSolPaymentInstructionData> {
  return getStructDecoder([
    ['discriminator', fixDecoderSize(getBytesDecoder(), 8)],
    ['amount', getU64Decoder()],
    ['startAt', getU64Decoder()],
    ['endAt', getU64Decoder()],
    ['interval', getU64Decoder()],
    ['payFees', getBooleanDecoder()],
  ]);
}

export function getCreateSolPaymentInstructionDataCodec(): Codec<
  CreateSolPaymentInstructionDataArgs,
  CreateSolPaymentInstructionData
> {
  return combineCodec(
    getCreateSolPaymentInstructionDataEncoder(),
    getCreateSolPaymentInstructionDataDecoder()
  );
}

export type CreateSolPaymentInput<
  TAccountSender extends string = string,
  TAccountRecipient extends string = string,
  TAccountSolPaymentAccount extends string = string,
  TAccountSystemProgram extends string = string,
> = {
  sender: TransactionSigner<TAccountSender>;
  recipient: Address<TAccountRecipient>;
  solPaymentAccount: TransactionSigner<TAccountSolPaymentAccount>;
  systemProgram?: Address<TAccountSystemProgram>;
  amount: CreateSolPaymentInstructionDataArgs['amount'];
  startAt: CreateSolPaymentInstructionDataArgs['startAt'];
  endAt: CreateSolPaymentInstructionDataArgs['endAt'];
  interval: CreateSolPaymentInstructionDataArgs['interval'];
  payFees: CreateSolPaymentInstructionDataArgs['payFees'];
};

export function getCreateSolPaymentInstruction<
  TAccountSender extends string,
  TAccountRecipient extends string,
  TAccountSolPaymentAccount extends string,
  TAccountSystemProgram extends string,
  TProgramAddress extends Address = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
>(
  input: CreateSolPaymentInput<
    TAccountSender,
    TAccountRecipient,
    TAccountSolPaymentAccount,
    TAccountSystemProgram
  >,
  config?: { programAddress?: TProgramAddress }
): CreateSolPaymentInstruction<
  TProgramAddress,
  TAccountSender,
  TAccountRecipient,
  TAccountSolPaymentAccount,
  TAccountSystemProgram
> {
  // Program address.
  const programAddress =
    config?.programAddress ?? HELIO_PROTOCOL_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    sender: { value: input.sender ?? null, isWritable: true },
    recipient: { value: input.recipient ?? null, isWritable: false },
    solPaymentAccount: {
      value: input.solPaymentAccount ?? null,
      isWritable: true,
    },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Resolve default values.
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value =
      '11111111111111111111111111111111' as Address<'11111111111111111111111111111111'>;
  }

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.sender),
      getAccountMeta(accounts.recipient),
      getAccountMeta(accounts.solPaymentAccount),
      getAccountMeta(accounts.systemProgram),
    ],
    programAddress,
    data: getCreateSolPaymentInstructionDataEncoder().encode(
      args as CreateSolPaymentInstructionDataArgs
    ),
  } as CreateSolPaymentInstruction<
    TProgramAddress,
    TAccountSender,
    TAccountRecipient,
    TAccountSolPaymentAccount,
    TAccountSystemProgram
  >;

  return instruction;
}

export type ParsedCreateSolPaymentInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    sender: TAccountMetas[0];
    recipient: TAccountMetas[1];
    solPaymentAccount: TAccountMetas[2];
    systemProgram: TAccountMetas[3];
  };
  data: CreateSolPaymentInstructionData;
};

export function parseCreateSolPaymentInstruction<
  TProgram extends string,
  TAccountMetas extends readonly IAccountMeta[],
>(
  instruction: IInstruction<TProgram> &
    IInstructionWithAccounts<TAccountMetas> &
    IInstructionWithData<Uint8Array>
): ParsedCreateSolPaymentInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 4) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts![accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      sender: getNextAccount(),
      recipient: getNextAccount(),
      solPaymentAccount: getNextAccount(),
      systemProgram: getNextAccount(),
    },
    data: getCreateSolPaymentInstructionDataDecoder().decode(instruction.data),
  };
}
