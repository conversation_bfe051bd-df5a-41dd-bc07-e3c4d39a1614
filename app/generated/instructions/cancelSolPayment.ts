/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  fixDecoderSize,
  fixEncoderSize,
  getBytesDecoder,
  getBytesEncoder,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  transformEncoder,
  type Address,
  type Codec,
  type Decoder,
  type Encoder,
  type IAccountMeta,
  type IAccountSignerMeta,
  type IInstruction,
  type IInstructionWithAccounts,
  type IInstructionWithData,
  type ReadonlyAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
  type WritableSignerAccount,
} from '@solana/kit';
import { HELIO_PROTOCOL_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const CANCEL_SOL_PAYMENT_DISCRIMINATOR = new Uint8Array([
  104, 88, 181, 132, 118, 25, 180, 229,
]);

export function getCancelSolPaymentDiscriminatorBytes() {
  return fixEncoderSize(getBytesEncoder(), 8).encode(
    CANCEL_SOL_PAYMENT_DISCRIMINATOR
  );
}

export type CancelSolPaymentInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountSigner extends string | IAccountMeta<string> = string,
  TAccountSender extends string | IAccountMeta<string> = string,
  TAccountRecipient extends string | IAccountMeta<string> = string,
  TAccountSolPaymentAccount extends string | IAccountMeta<string> = string,
  TAccountHelioFeeAccount extends string | IAccountMeta<string> = string,
  TAccountDaoFeeAccount extends string | IAccountMeta<string> = string,
  TAccountSystemProgram extends
    | string
    | IAccountMeta<string> = '11111111111111111111111111111111',
  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],
> = IInstruction<TProgram> &
  IInstructionWithData<Uint8Array> &
  IInstructionWithAccounts<
    [
      TAccountSigner extends string
        ? WritableSignerAccount<TAccountSigner> &
            IAccountSignerMeta<TAccountSigner>
        : TAccountSigner,
      TAccountSender extends string
        ? WritableAccount<TAccountSender>
        : TAccountSender,
      TAccountRecipient extends string
        ? WritableAccount<TAccountRecipient>
        : TAccountRecipient,
      TAccountSolPaymentAccount extends string
        ? WritableAccount<TAccountSolPaymentAccount>
        : TAccountSolPaymentAccount,
      TAccountHelioFeeAccount extends string
        ? WritableAccount<TAccountHelioFeeAccount>
        : TAccountHelioFeeAccount,
      TAccountDaoFeeAccount extends string
        ? WritableAccount<TAccountDaoFeeAccount>
        : TAccountDaoFeeAccount,
      TAccountSystemProgram extends string
        ? ReadonlyAccount<TAccountSystemProgram>
        : TAccountSystemProgram,
      ...TRemainingAccounts,
    ]
  >;

export type CancelSolPaymentInstructionData = {
  discriminator: ReadonlyUint8Array;
  baseFee: bigint;
};

export type CancelSolPaymentInstructionDataArgs = { baseFee: number | bigint };

export function getCancelSolPaymentInstructionDataEncoder(): Encoder<CancelSolPaymentInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', fixEncoderSize(getBytesEncoder(), 8)],
      ['baseFee', getU64Encoder()],
    ]),
    (value) => ({ ...value, discriminator: CANCEL_SOL_PAYMENT_DISCRIMINATOR })
  );
}

export function getCancelSolPaymentInstructionDataDecoder(): Decoder<CancelSolPaymentInstructionData> {
  return getStructDecoder([
    ['discriminator', fixDecoderSize(getBytesDecoder(), 8)],
    ['baseFee', getU64Decoder()],
  ]);
}

export function getCancelSolPaymentInstructionDataCodec(): Codec<
  CancelSolPaymentInstructionDataArgs,
  CancelSolPaymentInstructionData
> {
  return combineCodec(
    getCancelSolPaymentInstructionDataEncoder(),
    getCancelSolPaymentInstructionDataDecoder()
  );
}

export type CancelSolPaymentInput<
  TAccountSigner extends string = string,
  TAccountSender extends string = string,
  TAccountRecipient extends string = string,
  TAccountSolPaymentAccount extends string = string,
  TAccountHelioFeeAccount extends string = string,
  TAccountDaoFeeAccount extends string = string,
  TAccountSystemProgram extends string = string,
> = {
  signer: TransactionSigner<TAccountSigner>;
  sender: Address<TAccountSender>;
  recipient: Address<TAccountRecipient>;
  solPaymentAccount: Address<TAccountSolPaymentAccount>;
  helioFeeAccount: Address<TAccountHelioFeeAccount>;
  daoFeeAccount: Address<TAccountDaoFeeAccount>;
  systemProgram?: Address<TAccountSystemProgram>;
  baseFee: CancelSolPaymentInstructionDataArgs['baseFee'];
};

export function getCancelSolPaymentInstruction<
  TAccountSigner extends string,
  TAccountSender extends string,
  TAccountRecipient extends string,
  TAccountSolPaymentAccount extends string,
  TAccountHelioFeeAccount extends string,
  TAccountDaoFeeAccount extends string,
  TAccountSystemProgram extends string,
  TProgramAddress extends Address = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
>(
  input: CancelSolPaymentInput<
    TAccountSigner,
    TAccountSender,
    TAccountRecipient,
    TAccountSolPaymentAccount,
    TAccountHelioFeeAccount,
    TAccountDaoFeeAccount,
    TAccountSystemProgram
  >,
  config?: { programAddress?: TProgramAddress }
): CancelSolPaymentInstruction<
  TProgramAddress,
  TAccountSigner,
  TAccountSender,
  TAccountRecipient,
  TAccountSolPaymentAccount,
  TAccountHelioFeeAccount,
  TAccountDaoFeeAccount,
  TAccountSystemProgram
> {
  // Program address.
  const programAddress =
    config?.programAddress ?? HELIO_PROTOCOL_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    signer: { value: input.signer ?? null, isWritable: true },
    sender: { value: input.sender ?? null, isWritable: true },
    recipient: { value: input.recipient ?? null, isWritable: true },
    solPaymentAccount: {
      value: input.solPaymentAccount ?? null,
      isWritable: true,
    },
    helioFeeAccount: { value: input.helioFeeAccount ?? null, isWritable: true },
    daoFeeAccount: { value: input.daoFeeAccount ?? null, isWritable: true },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Resolve default values.
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value =
      '11111111111111111111111111111111' as Address<'11111111111111111111111111111111'>;
  }

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.signer),
      getAccountMeta(accounts.sender),
      getAccountMeta(accounts.recipient),
      getAccountMeta(accounts.solPaymentAccount),
      getAccountMeta(accounts.helioFeeAccount),
      getAccountMeta(accounts.daoFeeAccount),
      getAccountMeta(accounts.systemProgram),
    ],
    programAddress,
    data: getCancelSolPaymentInstructionDataEncoder().encode(
      args as CancelSolPaymentInstructionDataArgs
    ),
  } as CancelSolPaymentInstruction<
    TProgramAddress,
    TAccountSigner,
    TAccountSender,
    TAccountRecipient,
    TAccountSolPaymentAccount,
    TAccountHelioFeeAccount,
    TAccountDaoFeeAccount,
    TAccountSystemProgram
  >;

  return instruction;
}

export type ParsedCancelSolPaymentInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    signer: TAccountMetas[0];
    sender: TAccountMetas[1];
    recipient: TAccountMetas[2];
    solPaymentAccount: TAccountMetas[3];
    helioFeeAccount: TAccountMetas[4];
    daoFeeAccount: TAccountMetas[5];
    systemProgram: TAccountMetas[6];
  };
  data: CancelSolPaymentInstructionData;
};

export function parseCancelSolPaymentInstruction<
  TProgram extends string,
  TAccountMetas extends readonly IAccountMeta[],
>(
  instruction: IInstruction<TProgram> &
    IInstructionWithAccounts<TAccountMetas> &
    IInstructionWithData<Uint8Array>
): ParsedCancelSolPaymentInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 7) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts![accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      signer: getNextAccount(),
      sender: getNextAccount(),
      recipient: getNextAccount(),
      solPaymentAccount: getNextAccount(),
      helioFeeAccount: getNextAccount(),
      daoFeeAccount: getNextAccount(),
      systemProgram: getNextAccount(),
    },
    data: getCancelSolPaymentInstructionDataDecoder().decode(instruction.data),
  };
}
