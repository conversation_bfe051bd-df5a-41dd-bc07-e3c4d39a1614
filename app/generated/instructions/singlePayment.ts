/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  fixDecoderSize,
  fixEncoderSize,
  getArrayDecoder,
  getArrayEncoder,
  getBytesDecoder,
  getBytesEncoder,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  transformEncoder,
  type Address,
  type Codec,
  type Decoder,
  type Encoder,
  type IAccountMeta,
  type IAccountSignerMeta,
  type IInstruction,
  type IInstructionWithAccounts,
  type IInstructionWithData,
  type ReadonlyAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
  type WritableSignerAccount,
} from '@solana/kit';
import { HELIO_PROTOCOL_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const SINGLE_PAYMENT_DISCRIMINATOR = new Uint8Array([
  135, 168, 94, 207, 167, 43, 144, 221,
]);

export function getSinglePaymentDiscriminatorBytes() {
  return fixEncoderSize(getBytesEncoder(), 8).encode(
    SINGLE_PAYMENT_DISCRIMINATOR
  );
}

export type SinglePaymentInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountSender extends string | IAccountMeta<string> = string,
  TAccountSenderTokenAccount extends string | IAccountMeta<string> = string,
  TAccountRecipientTokenAccount extends string | IAccountMeta<string> = string,
  TAccountHelioFeeTokenAccount extends string | IAccountMeta<string> = string,
  TAccountDaoFeeTokenAccount extends string | IAccountMeta<string> = string,
  TAccountRecipient extends string | IAccountMeta<string> = string,
  TAccountHelioFeeAccount extends string | IAccountMeta<string> = string,
  TAccountDaoFeeAccount extends string | IAccountMeta<string> = string,
  TAccountMint extends string | IAccountMeta<string> = string,
  TAccountTokenProgram extends
    | string
    | IAccountMeta<string> = 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
  TAccountAssociatedTokenProgram extends string | IAccountMeta<string> = string,
  TAccountSystemProgram extends
    | string
    | IAccountMeta<string> = '11111111111111111111111111111111',
  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],
> = IInstruction<TProgram> &
  IInstructionWithData<Uint8Array> &
  IInstructionWithAccounts<
    [
      TAccountSender extends string
        ? WritableSignerAccount<TAccountSender> &
            IAccountSignerMeta<TAccountSender>
        : TAccountSender,
      TAccountSenderTokenAccount extends string
        ? WritableAccount<TAccountSenderTokenAccount>
        : TAccountSenderTokenAccount,
      TAccountRecipientTokenAccount extends string
        ? WritableAccount<TAccountRecipientTokenAccount>
        : TAccountRecipientTokenAccount,
      TAccountHelioFeeTokenAccount extends string
        ? WritableAccount<TAccountHelioFeeTokenAccount>
        : TAccountHelioFeeTokenAccount,
      TAccountDaoFeeTokenAccount extends string
        ? WritableAccount<TAccountDaoFeeTokenAccount>
        : TAccountDaoFeeTokenAccount,
      TAccountRecipient extends string
        ? ReadonlyAccount<TAccountRecipient>
        : TAccountRecipient,
      TAccountHelioFeeAccount extends string
        ? ReadonlyAccount<TAccountHelioFeeAccount>
        : TAccountHelioFeeAccount,
      TAccountDaoFeeAccount extends string
        ? ReadonlyAccount<TAccountDaoFeeAccount>
        : TAccountDaoFeeAccount,
      TAccountMint extends string
        ? ReadonlyAccount<TAccountMint>
        : TAccountMint,
      TAccountTokenProgram extends string
        ? ReadonlyAccount<TAccountTokenProgram>
        : TAccountTokenProgram,
      TAccountAssociatedTokenProgram extends string
        ? ReadonlyAccount<TAccountAssociatedTokenProgram>
        : TAccountAssociatedTokenProgram,
      TAccountSystemProgram extends string
        ? ReadonlyAccount<TAccountSystemProgram>
        : TAccountSystemProgram,
      ...TRemainingAccounts,
    ]
  >;

export type SinglePaymentInstructionData = {
  discriminator: ReadonlyUint8Array;
  amount: bigint;
  baseFee: bigint;
  remainingAmounts: Array<bigint>;
};

export type SinglePaymentInstructionDataArgs = {
  amount: number | bigint;
  baseFee: number | bigint;
  remainingAmounts: Array<number | bigint>;
};

export function getSinglePaymentInstructionDataEncoder(): Encoder<SinglePaymentInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', fixEncoderSize(getBytesEncoder(), 8)],
      ['amount', getU64Encoder()],
      ['baseFee', getU64Encoder()],
      ['remainingAmounts', getArrayEncoder(getU64Encoder())],
    ]),
    (value) => ({ ...value, discriminator: SINGLE_PAYMENT_DISCRIMINATOR })
  );
}

export function getSinglePaymentInstructionDataDecoder(): Decoder<SinglePaymentInstructionData> {
  return getStructDecoder([
    ['discriminator', fixDecoderSize(getBytesDecoder(), 8)],
    ['amount', getU64Decoder()],
    ['baseFee', getU64Decoder()],
    ['remainingAmounts', getArrayDecoder(getU64Decoder())],
  ]);
}

export function getSinglePaymentInstructionDataCodec(): Codec<
  SinglePaymentInstructionDataArgs,
  SinglePaymentInstructionData
> {
  return combineCodec(
    getSinglePaymentInstructionDataEncoder(),
    getSinglePaymentInstructionDataDecoder()
  );
}

export type SinglePaymentInput<
  TAccountSender extends string = string,
  TAccountSenderTokenAccount extends string = string,
  TAccountRecipientTokenAccount extends string = string,
  TAccountHelioFeeTokenAccount extends string = string,
  TAccountDaoFeeTokenAccount extends string = string,
  TAccountRecipient extends string = string,
  TAccountHelioFeeAccount extends string = string,
  TAccountDaoFeeAccount extends string = string,
  TAccountMint extends string = string,
  TAccountTokenProgram extends string = string,
  TAccountAssociatedTokenProgram extends string = string,
  TAccountSystemProgram extends string = string,
> = {
  sender: TransactionSigner<TAccountSender>;
  senderTokenAccount: Address<TAccountSenderTokenAccount>;
  recipientTokenAccount: Address<TAccountRecipientTokenAccount>;
  helioFeeTokenAccount: Address<TAccountHelioFeeTokenAccount>;
  daoFeeTokenAccount: Address<TAccountDaoFeeTokenAccount>;
  recipient: Address<TAccountRecipient>;
  helioFeeAccount: Address<TAccountHelioFeeAccount>;
  daoFeeAccount: Address<TAccountDaoFeeAccount>;
  mint: Address<TAccountMint>;
  tokenProgram?: Address<TAccountTokenProgram>;
  associatedTokenProgram: Address<TAccountAssociatedTokenProgram>;
  systemProgram?: Address<TAccountSystemProgram>;
  amount: SinglePaymentInstructionDataArgs['amount'];
  baseFee: SinglePaymentInstructionDataArgs['baseFee'];
  remainingAmounts: SinglePaymentInstructionDataArgs['remainingAmounts'];
};

export function getSinglePaymentInstruction<
  TAccountSender extends string,
  TAccountSenderTokenAccount extends string,
  TAccountRecipientTokenAccount extends string,
  TAccountHelioFeeTokenAccount extends string,
  TAccountDaoFeeTokenAccount extends string,
  TAccountRecipient extends string,
  TAccountHelioFeeAccount extends string,
  TAccountDaoFeeAccount extends string,
  TAccountMint extends string,
  TAccountTokenProgram extends string,
  TAccountAssociatedTokenProgram extends string,
  TAccountSystemProgram extends string,
  TProgramAddress extends Address = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
>(
  input: SinglePaymentInput<
    TAccountSender,
    TAccountSenderTokenAccount,
    TAccountRecipientTokenAccount,
    TAccountHelioFeeTokenAccount,
    TAccountDaoFeeTokenAccount,
    TAccountRecipient,
    TAccountHelioFeeAccount,
    TAccountDaoFeeAccount,
    TAccountMint,
    TAccountTokenProgram,
    TAccountAssociatedTokenProgram,
    TAccountSystemProgram
  >,
  config?: { programAddress?: TProgramAddress }
): SinglePaymentInstruction<
  TProgramAddress,
  TAccountSender,
  TAccountSenderTokenAccount,
  TAccountRecipientTokenAccount,
  TAccountHelioFeeTokenAccount,
  TAccountDaoFeeTokenAccount,
  TAccountRecipient,
  TAccountHelioFeeAccount,
  TAccountDaoFeeAccount,
  TAccountMint,
  TAccountTokenProgram,
  TAccountAssociatedTokenProgram,
  TAccountSystemProgram
> {
  // Program address.
  const programAddress =
    config?.programAddress ?? HELIO_PROTOCOL_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    sender: { value: input.sender ?? null, isWritable: true },
    senderTokenAccount: {
      value: input.senderTokenAccount ?? null,
      isWritable: true,
    },
    recipientTokenAccount: {
      value: input.recipientTokenAccount ?? null,
      isWritable: true,
    },
    helioFeeTokenAccount: {
      value: input.helioFeeTokenAccount ?? null,
      isWritable: true,
    },
    daoFeeTokenAccount: {
      value: input.daoFeeTokenAccount ?? null,
      isWritable: true,
    },
    recipient: { value: input.recipient ?? null, isWritable: false },
    helioFeeAccount: {
      value: input.helioFeeAccount ?? null,
      isWritable: false,
    },
    daoFeeAccount: { value: input.daoFeeAccount ?? null, isWritable: false },
    mint: { value: input.mint ?? null, isWritable: false },
    tokenProgram: { value: input.tokenProgram ?? null, isWritable: false },
    associatedTokenProgram: {
      value: input.associatedTokenProgram ?? null,
      isWritable: false,
    },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Resolve default values.
  if (!accounts.tokenProgram.value) {
    accounts.tokenProgram.value =
      'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' as Address<'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'>;
  }
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value =
      '11111111111111111111111111111111' as Address<'11111111111111111111111111111111'>;
  }

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.sender),
      getAccountMeta(accounts.senderTokenAccount),
      getAccountMeta(accounts.recipientTokenAccount),
      getAccountMeta(accounts.helioFeeTokenAccount),
      getAccountMeta(accounts.daoFeeTokenAccount),
      getAccountMeta(accounts.recipient),
      getAccountMeta(accounts.helioFeeAccount),
      getAccountMeta(accounts.daoFeeAccount),
      getAccountMeta(accounts.mint),
      getAccountMeta(accounts.tokenProgram),
      getAccountMeta(accounts.associatedTokenProgram),
      getAccountMeta(accounts.systemProgram),
    ],
    programAddress,
    data: getSinglePaymentInstructionDataEncoder().encode(
      args as SinglePaymentInstructionDataArgs
    ),
  } as SinglePaymentInstruction<
    TProgramAddress,
    TAccountSender,
    TAccountSenderTokenAccount,
    TAccountRecipientTokenAccount,
    TAccountHelioFeeTokenAccount,
    TAccountDaoFeeTokenAccount,
    TAccountRecipient,
    TAccountHelioFeeAccount,
    TAccountDaoFeeAccount,
    TAccountMint,
    TAccountTokenProgram,
    TAccountAssociatedTokenProgram,
    TAccountSystemProgram
  >;

  return instruction;
}

export type ParsedSinglePaymentInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    sender: TAccountMetas[0];
    senderTokenAccount: TAccountMetas[1];
    recipientTokenAccount: TAccountMetas[2];
    helioFeeTokenAccount: TAccountMetas[3];
    daoFeeTokenAccount: TAccountMetas[4];
    recipient: TAccountMetas[5];
    helioFeeAccount: TAccountMetas[6];
    daoFeeAccount: TAccountMetas[7];
    mint: TAccountMetas[8];
    tokenProgram: TAccountMetas[9];
    associatedTokenProgram: TAccountMetas[10];
    systemProgram: TAccountMetas[11];
  };
  data: SinglePaymentInstructionData;
};

export function parseSinglePaymentInstruction<
  TProgram extends string,
  TAccountMetas extends readonly IAccountMeta[],
>(
  instruction: IInstruction<TProgram> &
    IInstructionWithAccounts<TAccountMetas> &
    IInstructionWithData<Uint8Array>
): ParsedSinglePaymentInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 12) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts![accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      sender: getNextAccount(),
      senderTokenAccount: getNextAccount(),
      recipientTokenAccount: getNextAccount(),
      helioFeeTokenAccount: getNextAccount(),
      daoFeeTokenAccount: getNextAccount(),
      recipient: getNextAccount(),
      helioFeeAccount: getNextAccount(),
      daoFeeAccount: getNextAccount(),
      mint: getNextAccount(),
      tokenProgram: getNextAccount(),
      associatedTokenProgram: getNextAccount(),
      systemProgram: getNextAccount(),
    },
    data: getSinglePaymentInstructionDataDecoder().decode(instruction.data),
  };
}
