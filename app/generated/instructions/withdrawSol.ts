/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */

import {
  combineCodec,
  fixDecoderSize,
  fixEncoderSize,
  getBytesDecoder,
  getBytesEncoder,
  getStructDecoder,
  getStructEncoder,
  getU64Decoder,
  getU64Encoder,
  transformEncoder,
  type Address,
  type Codec,
  type Decoder,
  type Encoder,
  type IAccountMeta,
  type IAccountSignerMeta,
  type IInstruction,
  type IInstructionWithAccounts,
  type IInstructionWithData,
  type ReadonlyAccount,
  type ReadonlyUint8Array,
  type TransactionSigner,
  type WritableAccount,
  type WritableSignerAccount,
} from '@solana/kit';
import { HELIO_PROTOCOL_PROGRAM_ADDRESS } from '../programs';
import { getAccountMetaFactory, type ResolvedAccount } from '../shared';

export const WITHDRAW_SOL_DISCRIMINATOR = new Uint8Array([
  145, 131, 74, 136, 65, 137, 42, 38,
]);

export function getWithdrawSolDiscriminatorBytes() {
  return fixEncoderSize(getBytesEncoder(), 8).encode(
    WITHDRAW_SOL_DISCRIMINATOR
  );
}

export type WithdrawSolInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountSigner extends string | IAccountMeta<string> = string,
  TAccountRecipient extends string | IAccountMeta<string> = string,
  TAccountSolPaymentAccount extends string | IAccountMeta<string> = string,
  TAccountHelioFeeAccount extends string | IAccountMeta<string> = string,
  TAccountDaoFeeAccount extends string | IAccountMeta<string> = string,
  TAccountSystemProgram extends
    | string
    | IAccountMeta<string> = '11111111111111111111111111111111',
  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],
> = IInstruction<TProgram> &
  IInstructionWithData<Uint8Array> &
  IInstructionWithAccounts<
    [
      TAccountSigner extends string
        ? WritableSignerAccount<TAccountSigner> &
            IAccountSignerMeta<TAccountSigner>
        : TAccountSigner,
      TAccountRecipient extends string
        ? WritableAccount<TAccountRecipient>
        : TAccountRecipient,
      TAccountSolPaymentAccount extends string
        ? WritableAccount<TAccountSolPaymentAccount>
        : TAccountSolPaymentAccount,
      TAccountHelioFeeAccount extends string
        ? WritableAccount<TAccountHelioFeeAccount>
        : TAccountHelioFeeAccount,
      TAccountDaoFeeAccount extends string
        ? WritableAccount<TAccountDaoFeeAccount>
        : TAccountDaoFeeAccount,
      TAccountSystemProgram extends string
        ? ReadonlyAccount<TAccountSystemProgram>
        : TAccountSystemProgram,
      ...TRemainingAccounts,
    ]
  >;

export type WithdrawSolInstructionData = {
  discriminator: ReadonlyUint8Array;
  baseFee: bigint;
};

export type WithdrawSolInstructionDataArgs = { baseFee: number | bigint };

export function getWithdrawSolInstructionDataEncoder(): Encoder<WithdrawSolInstructionDataArgs> {
  return transformEncoder(
    getStructEncoder([
      ['discriminator', fixEncoderSize(getBytesEncoder(), 8)],
      ['baseFee', getU64Encoder()],
    ]),
    (value) => ({ ...value, discriminator: WITHDRAW_SOL_DISCRIMINATOR })
  );
}

export function getWithdrawSolInstructionDataDecoder(): Decoder<WithdrawSolInstructionData> {
  return getStructDecoder([
    ['discriminator', fixDecoderSize(getBytesDecoder(), 8)],
    ['baseFee', getU64Decoder()],
  ]);
}

export function getWithdrawSolInstructionDataCodec(): Codec<
  WithdrawSolInstructionDataArgs,
  WithdrawSolInstructionData
> {
  return combineCodec(
    getWithdrawSolInstructionDataEncoder(),
    getWithdrawSolInstructionDataDecoder()
  );
}

export type WithdrawSolInput<
  TAccountSigner extends string = string,
  TAccountRecipient extends string = string,
  TAccountSolPaymentAccount extends string = string,
  TAccountHelioFeeAccount extends string = string,
  TAccountDaoFeeAccount extends string = string,
  TAccountSystemProgram extends string = string,
> = {
  signer: TransactionSigner<TAccountSigner>;
  recipient: Address<TAccountRecipient>;
  solPaymentAccount: Address<TAccountSolPaymentAccount>;
  helioFeeAccount: Address<TAccountHelioFeeAccount>;
  daoFeeAccount: Address<TAccountDaoFeeAccount>;
  systemProgram?: Address<TAccountSystemProgram>;
  baseFee: WithdrawSolInstructionDataArgs['baseFee'];
};

export function getWithdrawSolInstruction<
  TAccountSigner extends string,
  TAccountRecipient extends string,
  TAccountSolPaymentAccount extends string,
  TAccountHelioFeeAccount extends string,
  TAccountDaoFeeAccount extends string,
  TAccountSystemProgram extends string,
  TProgramAddress extends Address = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
>(
  input: WithdrawSolInput<
    TAccountSigner,
    TAccountRecipient,
    TAccountSolPaymentAccount,
    TAccountHelioFeeAccount,
    TAccountDaoFeeAccount,
    TAccountSystemProgram
  >,
  config?: { programAddress?: TProgramAddress }
): WithdrawSolInstruction<
  TProgramAddress,
  TAccountSigner,
  TAccountRecipient,
  TAccountSolPaymentAccount,
  TAccountHelioFeeAccount,
  TAccountDaoFeeAccount,
  TAccountSystemProgram
> {
  // Program address.
  const programAddress =
    config?.programAddress ?? HELIO_PROTOCOL_PROGRAM_ADDRESS;

  // Original accounts.
  const originalAccounts = {
    signer: { value: input.signer ?? null, isWritable: true },
    recipient: { value: input.recipient ?? null, isWritable: true },
    solPaymentAccount: {
      value: input.solPaymentAccount ?? null,
      isWritable: true,
    },
    helioFeeAccount: { value: input.helioFeeAccount ?? null, isWritable: true },
    daoFeeAccount: { value: input.daoFeeAccount ?? null, isWritable: true },
    systemProgram: { value: input.systemProgram ?? null, isWritable: false },
  };
  const accounts = originalAccounts as Record<
    keyof typeof originalAccounts,
    ResolvedAccount
  >;

  // Original args.
  const args = { ...input };

  // Resolve default values.
  if (!accounts.systemProgram.value) {
    accounts.systemProgram.value =
      '11111111111111111111111111111111' as Address<'11111111111111111111111111111111'>;
  }

  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');
  const instruction = {
    accounts: [
      getAccountMeta(accounts.signer),
      getAccountMeta(accounts.recipient),
      getAccountMeta(accounts.solPaymentAccount),
      getAccountMeta(accounts.helioFeeAccount),
      getAccountMeta(accounts.daoFeeAccount),
      getAccountMeta(accounts.systemProgram),
    ],
    programAddress,
    data: getWithdrawSolInstructionDataEncoder().encode(
      args as WithdrawSolInstructionDataArgs
    ),
  } as WithdrawSolInstruction<
    TProgramAddress,
    TAccountSigner,
    TAccountRecipient,
    TAccountSolPaymentAccount,
    TAccountHelioFeeAccount,
    TAccountDaoFeeAccount,
    TAccountSystemProgram
  >;

  return instruction;
}

export type ParsedWithdrawSolInstruction<
  TProgram extends string = typeof HELIO_PROTOCOL_PROGRAM_ADDRESS,
  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],
> = {
  programAddress: Address<TProgram>;
  accounts: {
    signer: TAccountMetas[0];
    recipient: TAccountMetas[1];
    solPaymentAccount: TAccountMetas[2];
    helioFeeAccount: TAccountMetas[3];
    daoFeeAccount: TAccountMetas[4];
    systemProgram: TAccountMetas[5];
  };
  data: WithdrawSolInstructionData;
};

export function parseWithdrawSolInstruction<
  TProgram extends string,
  TAccountMetas extends readonly IAccountMeta[],
>(
  instruction: IInstruction<TProgram> &
    IInstructionWithAccounts<TAccountMetas> &
    IInstructionWithData<Uint8Array>
): ParsedWithdrawSolInstruction<TProgram, TAccountMetas> {
  if (instruction.accounts.length < 6) {
    // TODO: Coded error.
    throw new Error('Not enough accounts');
  }
  let accountIndex = 0;
  const getNextAccount = () => {
    const accountMeta = instruction.accounts![accountIndex]!;
    accountIndex += 1;
    return accountMeta;
  };
  return {
    programAddress: instruction.programAddress,
    accounts: {
      signer: getNextAccount(),
      recipient: getNextAccount(),
      solPaymentAccount: getNextAccount(),
      helioFeeAccount: getNextAccount(),
      daoFeeAccount: getNextAccount(),
      systemProgram: getNextAccount(),
    },
    data: getWithdrawSolInstructionDataDecoder().decode(instruction.data),
  };
}
