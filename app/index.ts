import { highlight } from '@kdt310722/logger'
import { notNullish } from '@kdt310722/utils/common'
import { formatNanoseconds } from '@kdt310722/utils/number'
import { shorten } from '@kdt310722/utils/string'
import { formatDate } from '@kdt310722/utils/time'
import { logger } from './core/logger'
import { logs, subscribe } from './core/logs'
import { monitor } from './core/monitor'
import { server } from './core/server'

logs.on('payment', (data) => {
    server.broadcast('payment', data)

    const now = Date.now()
    const processTime = BigInt((now - data.receivedAt) * 1e6)

    const message = [
        'New payment transaction:',
        `  + TX: ${highlight(shorten(data.signature, 4))}`,
        `  + Slot: ${highlight(data.slot)}`,
        `  + Source: ${highlight(data.datasource)}`,
        `  + Received At: ${highlight(formatDate(new Date(data.receivedAt), true))}`,
        `  + Process Time: ${highlight(processTime > 0n ? formatNanoseconds(processTime) : '0ms')}`,
    ]

    if (notNullish(data.blockTime)) {
        message.push(`  + Block Time: ${highlight(formatDate(new Date(data.blockTime * 1000), true))}`)
    }

    if (notNullish(data.nodeTime)) {
        message.push(
            `  + Node Time: ${highlight(formatDate(new Date(data.nodeTime), true))}`,
            `  + Latency: ${highlight(formatNanoseconds(BigInt((data.receivedAt - data.nodeTime) * 1e6)))}`,
        )
    }

    logger.info(message.join('\n'))
})

server.start().then(() => subscribe()).then(() => monitor()).catch((error) => {
    logger.exit(1, 'fatal', 'Failed to start application', error)
})
