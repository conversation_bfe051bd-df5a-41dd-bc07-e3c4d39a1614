{"accounts": [{"docs": ["Account hold,ing metadata for payment stream."], "name": "PaymentAccount", "type": {"fields": [{"docs": ["The raw amount to transfer"], "name": "amount", "type": "u64"}, {"docs": ["The recipents withrawed amount"], "name": "withdrawal", "type": "u64"}, {"docs": ["The unix timestamp to start the payment at"], "name": "startAt", "type": "u64"}, {"docs": ["The unix timestamp to end the payment at. If recurrence_interval is zero, then start_at must equal end_at"], "name": "endAt", "type": "u64"}, {"docs": ["Duration of charged interval in seconds"], "name": "interval", "type": "u64"}, {"docs": ["The sender party of the payment"], "name": "sender<PERSON><PERSON>", "type": "public<PERSON>ey"}, {"docs": ["The sender's token account address"], "name": "senderTokens", "type": "public<PERSON>ey"}, {"docs": ["The recipient party of the payment"], "name": "<PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"docs": ["The recipient's token account address"], "name": "recipientTokens", "type": "public<PERSON>ey"}, {"docs": ["The mint (currency) token of the payment"], "name": "mint", "type": "public<PERSON>ey"}, {"docs": ["PDA bump (0-255, sent from client on stream creation)"], "name": "bump", "type": "u8"}, {"docs": ["Pay fees flag"], "name": "payFees", "type": "bool"}], "kind": "struct"}}, {"docs": ["Account holding metadata for sol payment stream."], "name": "SolPaymentAccount", "type": {"fields": [{"docs": ["The total amount to transfer"], "name": "amount", "type": "u64"}, {"docs": ["The recipents withrawed amount"], "name": "withdrawal", "type": "u64"}, {"docs": ["The unix timestamp to start the payment at"], "name": "startAt", "type": "u64"}, {"docs": ["The unix timestamp to end the payment at. If recurrence_interval is zero, then start_at must equal end_at"], "name": "endAt", "type": "u64"}, {"docs": ["Duration of charged interval in seconds"], "name": "interval", "type": "u64"}, {"docs": ["The sender party of the payment"], "name": "sender<PERSON><PERSON>", "type": "public<PERSON>ey"}, {"docs": ["The recipient party of the payment"], "name": "<PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"docs": ["Pay fees flag"], "name": "payFees", "type": "bool"}], "kind": "struct"}}], "docs": ["Program for processing stream and one time payments in SOL or SPL token."], "errors": [{"code": 6000, "msg": "Insufficient SOL to pay transfer fees.", "name": "InsufficientBalance"}, {"code": 6001, "msg": "The timestamps must be chronological.", "name": "InvalidChronology"}, {"code": 6002, "msg": "The amount must be positive number.", "name": "InvalidAmount"}, {"code": 6003, "msg": "The charging period must be shorter then duration time, longer then 10secs.", "name": "InvalidPeriod"}, {"code": 6004, "msg": "The account is empty, nothing to withdraw.", "name": "EmptyAccount"}, {"code": 6005, "msg": "The signer trying to cancel running stream is not a sender!", "name": "CancelAuthority"}, {"code": 6006, "msg": "The number is invalid!", "name": "InvalidNumber"}, {"code": 6007, "msg": "The token account not provided or has wrong owner.", "name": "InvalidTokenAccount"}, {"code": 6008, "msg": "Remainings amounts and accounts not matching.", "name": "InvalidSplitPaymentData"}, {"code": 6009, "msg": "Invalid payment token account.", "name": "InvalidPaymentTokenAccount"}, {"code": 6010, "msg": "Invalid PDA signer address.", "name": "Invalid<PERSON><PERSON><PERSON>er"}, {"code": 6011, "msg": "Invalid fee account address.", "name": "Invalid<PERSON>ee<PERSON><PERSON>unt"}, {"code": 6012, "msg": "Stream already expired no changes allowed.", "name": "StreamExpired"}, {"code": 6013, "msg": "Invalid topup amount (less then base interval amount).", "name": "InvalidTopupAmount"}, {"code": 6014, "msg": "Invalid fee, 100 percent or larger.", "name": "InvalidFee"}, {"code": 6015, "msg": "Signer not authorized to perform operation.", "name": "Unauthorized<PERSON><PERSON><PERSON>"}, {"code": 6016, "msg": "General error", "name": "General"}], "events": [{"fields": [{"index": false, "name": "transferAmount", "type": "u64"}, {"index": false, "name": "fee", "type": "u64"}, {"index": false, "name": "sender", "type": "public<PERSON>ey"}, {"index": false, "name": "recipient", "type": "public<PERSON>ey"}, {"index": true, "name": "label", "type": "string"}], "name": "SinglePaymentEvent"}], "instructions": [{"accounts": [{"isMut": true, "isSigner": true, "name": "sender"}, {"isMut": true, "isSigner": false, "name": "senderTokenAccount"}, {"isMut": true, "isSigner": true, "name": "paymentAccount"}, {"isMut": true, "isSigner": false, "name": "paymentTokenAccount"}, {"isMut": true, "isSigner": false, "name": "recipientTokenAccount"}, {"isMut": true, "isSigner": false, "name": "helioFeeTokenAccount"}, {"isMut": true, "isSigner": false, "name": "dao<PERSON><PERSON><PERSON><PERSON>Account"}, {"isMut": false, "isSigner": false, "name": "recipient"}, {"isMut": false, "isSigner": false, "name": "helioFeeAccount"}, {"isMut": false, "isSigner": false, "name": "dao<PERSON><PERSON><PERSON><PERSON>unt"}, {"isMut": false, "isSigner": false, "name": "mint"}, {"isMut": false, "isSigner": false, "name": "tokenProgram"}, {"isMut": false, "isSigner": false, "name": "associatedTokenProgram"}, {"isMut": false, "isSigner": false, "name": "systemProgram"}], "args": [{"name": "amount", "type": "u64"}, {"name": "startAt", "type": "u64"}, {"name": "endAt", "type": "u64"}, {"name": "interval", "type": "u64"}, {"name": "bump", "type": "u8"}, {"name": "payFees", "type": "bool"}], "docs": ["Entry point for function for creating the stream payment."], "name": "createPayment"}, {"accounts": [{"isMut": true, "isSigner": true, "name": "sender"}, {"isMut": false, "isSigner": false, "name": "recipient"}, {"isMut": true, "isSigner": true, "name": "solPaymentAccount"}, {"isMut": false, "isSigner": false, "name": "systemProgram"}], "args": [{"name": "amount", "type": "u64"}, {"name": "startAt", "type": "u64"}, {"name": "endAt", "type": "u64"}, {"name": "interval", "type": "u64"}, {"name": "payFees", "type": "bool"}], "docs": ["Entry point for function for creating the stream payment in SOL."], "name": "createSolPayment"}, {"accounts": [{"isMut": true, "isSigner": true, "name": "signer"}, {"isMut": true, "isSigner": false, "name": "sender"}, {"isMut": true, "isSigner": false, "name": "senderTokenAccount"}, {"isMut": true, "isSigner": false, "name": "recipient"}, {"isMut": true, "isSigner": false, "name": "recipientTokenAccount"}, {"isMut": true, "isSigner": false, "name": "paymentAccount"}, {"isMut": true, "isSigner": false, "name": "paymentTokenAccount"}, {"isMut": true, "isSigner": false, "name": "helioFeeTokenAccount"}, {"isMut": true, "isSigner": false, "name": "dao<PERSON><PERSON><PERSON><PERSON>Account"}, {"isMut": false, "isSigner": false, "name": "helioFeeAccount"}, {"isMut": false, "isSigner": false, "name": "dao<PERSON><PERSON><PERSON><PERSON>unt"}, {"isMut": false, "isSigner": false, "name": "p<PERSON><PERSON><PERSON><PERSON>"}, {"isMut": false, "isSigner": false, "name": "mint"}, {"isMut": false, "isSigner": false, "name": "tokenProgram"}, {"isMut": false, "isSigner": false, "name": "systemProgram"}], "args": [{"name": "baseFee", "type": "u64"}], "docs": ["Entry point for cancelling(terminating) the stream payment."], "name": "cancelPayment"}, {"accounts": [{"isMut": true, "isSigner": true, "name": "signer"}, {"isMut": true, "isSigner": false, "name": "sender"}, {"isMut": true, "isSigner": false, "name": "recipient"}, {"isMut": true, "isSigner": false, "name": "solPaymentAccount"}, {"isMut": true, "isSigner": false, "name": "helioFeeAccount"}, {"isMut": true, "isSigner": false, "name": "dao<PERSON><PERSON><PERSON><PERSON>unt"}, {"isMut": false, "isSigner": false, "name": "systemProgram"}], "args": [{"name": "baseFee", "type": "u64"}], "docs": ["Entry point for cancelling(terminating) the stream payment."], "name": "cancelSolPayment"}, {"accounts": [{"isMut": true, "isSigner": true, "name": "signer"}, {"isMut": true, "isSigner": false, "name": "recipient"}, {"isMut": true, "isSigner": false, "name": "recipientTokenAccount"}, {"isMut": true, "isSigner": false, "name": "paymentAccount"}, {"isMut": true, "isSigner": false, "name": "paymentTokenAccount"}, {"isMut": true, "isSigner": false, "name": "helioFeeTokenAccount"}, {"isMut": true, "isSigner": false, "name": "dao<PERSON><PERSON><PERSON><PERSON>Account"}, {"isMut": false, "isSigner": false, "name": "helioFeeAccount"}, {"isMut": false, "isSigner": false, "name": "dao<PERSON><PERSON><PERSON><PERSON>unt"}, {"isMut": false, "isSigner": false, "name": "p<PERSON><PERSON><PERSON><PERSON>"}, {"isMut": false, "isSigner": false, "name": "mint"}, {"isMut": false, "isSigner": false, "name": "tokenProgram"}, {"isMut": false, "isSigner": false, "name": "systemProgram"}], "args": [{"name": "baseFee", "type": "u64"}], "docs": ["Entry point for witdhraw due SPL tokens for recipient."], "name": "withdraw"}, {"accounts": [{"isMut": true, "isSigner": true, "name": "signer"}, {"isMut": true, "isSigner": false, "name": "recipient"}, {"isMut": true, "isSigner": false, "name": "solPaymentAccount"}, {"isMut": true, "isSigner": false, "name": "helioFeeAccount"}, {"isMut": true, "isSigner": false, "name": "dao<PERSON><PERSON><PERSON><PERSON>unt"}, {"isMut": false, "isSigner": false, "name": "systemProgram"}], "args": [{"name": "baseFee", "type": "u64"}], "docs": ["Entry point for wit<PERSON><PERSON> due SOL for recipient."], "name": "withdrawSol"}, {"accounts": [{"isMut": true, "isSigner": true, "name": "sender"}, {"isMut": true, "isSigner": false, "name": "senderTokenAccount"}, {"isMut": true, "isSigner": false, "name": "recipientTokenAccount"}, {"isMut": true, "isSigner": false, "name": "helioFeeTokenAccount"}, {"isMut": true, "isSigner": false, "name": "dao<PERSON><PERSON><PERSON><PERSON>Account"}, {"isMut": false, "isSigner": false, "name": "recipient"}, {"isMut": false, "isSigner": false, "name": "helioFeeAccount"}, {"isMut": false, "isSigner": false, "name": "dao<PERSON><PERSON><PERSON><PERSON>unt"}, {"isMut": false, "isSigner": false, "name": "mint"}, {"isMut": false, "isSigner": false, "name": "tokenProgram"}, {"isMut": false, "isSigner": false, "name": "associatedTokenProgram"}, {"isMut": false, "isSigner": false, "name": "systemProgram"}], "args": [{"name": "amount", "type": "u64"}, {"name": "baseFee", "type": "u64"}, {"name": "remainingAmounts", "type": {"vec": "u64"}}], "docs": ["Entry point for one time payment in SPL tokens."], "name": "singlePayment"}, {"accounts": [{"isMut": true, "isSigner": true, "name": "sender"}, {"isMut": true, "isSigner": false, "name": "recipient"}, {"isMut": true, "isSigner": false, "name": "helioFeeAccount"}, {"isMut": true, "isSigner": false, "name": "dao<PERSON><PERSON><PERSON><PERSON>unt"}, {"isMut": false, "isSigner": false, "name": "systemProgram"}], "args": [{"name": "amount", "type": "u64"}, {"name": "baseFee", "type": "u64"}, {"name": "remainingAmounts", "type": {"vec": "u64"}}], "docs": ["Entry point for one time payment in SOL."], "name": "singleSolPayment"}, {"accounts": [{"isMut": true, "isSigner": false, "name": "sender"}, {"isMut": true, "isSigner": false, "name": "senderTokenAccount"}, {"isMut": true, "isSigner": false, "name": "paymentAccount"}, {"isMut": true, "isSigner": false, "name": "paymentTokenAccount"}, {"isMut": false, "isSigner": false, "name": "p<PERSON><PERSON><PERSON><PERSON>"}, {"isMut": false, "isSigner": false, "name": "mint"}, {"isMut": false, "isSigner": false, "name": "tokenProgram"}], "args": [{"name": "topupAmount", "type": "u64"}], "docs": ["Entry point for topup in SPL tokens."], "name": "topup"}, {"accounts": [{"isMut": true, "isSigner": false, "name": "sender"}, {"isMut": true, "isSigner": false, "name": "solPaymentAccount"}, {"isMut": false, "isSigner": false, "name": "systemProgram"}], "args": [{"name": "topupAmount", "type": "u64"}], "docs": ["Entry point for topup in SPL tokens."], "name": "topupSol"}], "name": "helio_protocol", "version": "1.0.1"}